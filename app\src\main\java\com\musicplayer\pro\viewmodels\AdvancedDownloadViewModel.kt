package com.musicplayer.pro.viewmodels

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.musicplayer.pro.database.MusicDatabase
import com.musicplayer.pro.download.AdvancedDownloadManager
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.models.DownloadStatus
import com.musicplayer.pro.repositories.AdvancedMusicRepository
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel متقدم للتحميل مع جميع الميزات المطلوبة
 */
class AdvancedDownloadViewModel(application: Application) : AndroidViewModel(application) {
    
    // قاعدة البيانات والمستودع
    private val database = MusicDatabase.getDatabase(application, viewModelScope)
    private val musicRepository = AdvancedMusicRepository(application, database)
    
    // مدير التحميل المتقدم
    private val downloadManager = AdvancedDownloadManager(application, database)
    
    // LiveData للتحميلات
    val downloads: LiveData<List<Download>> = downloadManager.downloads.asLiveData()
    val downloadProgress: LiveData<Map<String, Int>> = downloadManager.downloadProgress
    
    // حالة التطبيق
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _message = MutableLiveData<String>()
    val message: LiveData<String> = _message
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    // إحصائيات التحميل
    private val _downloadStats = MutableLiveData<DownloadStatistics>()
    val downloadStats: LiveData<DownloadStatistics> = _downloadStats
    
    init {
        setupDownloadManager()
        updateStatistics()
    }
    
    /**
     * إعداد مدير التحميل
     */
    private fun setupDownloadManager() {
        // إعداد مستمع اكتمال التحميل
        downloadManager.setOnDownloadCompletedListener { download ->
            _message.value = "تم اكتمال تحميل: ${download.title}"
            updateStatistics()
            
            // إشعار تحديث مكتبة الأغاني
            notifyMusicLibraryUpdate()
        }
        
        // إعداد مستمع فشل التحميل
        downloadManager.setOnDownloadFailedListener { download, error ->
            _errorMessage.value = "فشل تحميل ${download.title}: $error"
            updateStatistics()
        }
    }
    
    /**
     * بدء تحميل جديد
     */
    fun startDownload(url: String, quality: String = "best", format: String = "mp3") {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _message.value = "جاري بدء التحميل..."
                
                // التحقق من صحة الرابط
                if (!isValidUrl(url)) {
                    _errorMessage.value = "رابط غير صحيح"
                    return@launch
                }
                
                // بدء التحميل
                val result = downloadManager.startDownload(url, quality, format)
                
                if (result.isSuccess) {
                    val downloadId = result.getOrNull()
                    _message.value = "تم بدء التحميل بنجاح"
                    android.util.Log.d("AdvancedDownloadViewModel", "بدء التحميل: $downloadId")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "خطأ غير معروف"
                    _errorMessage.value = "فشل في بدء التحميل: $error"
                }
                
            } catch (e: Exception) {
                _errorMessage.value = "خطأ في بدء التحميل: ${e.message}"
                android.util.Log.e("AdvancedDownloadViewModel", "خطأ في بدء التحميل", e)
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * إيقاف تحميل
     */
    fun pauseDownload(downloadId: String) {
        downloadManager.pauseDownload(downloadId)
        _message.value = "تم إيقاف التحميل"
    }
    
    /**
     * استئناف تحميل
     */
    fun resumeDownload(downloadId: String) {
        downloadManager.resumeDownload(downloadId)
        _message.value = "تم استئناف التحميل"
    }
    
    /**
     * إلغاء تحميل
     */
    fun cancelDownload(downloadId: String) {
        downloadManager.cancelDownload(downloadId)
        _message.value = "تم إلغاء التحميل"
        updateStatistics()
    }
    
    /**
     * حذف تحميل
     */
    fun deleteDownload(downloadId: String) {
        downloadManager.deleteDownload(downloadId)
        _message.value = "تم حذف التحميل"
        updateStatistics()
    }
    
    /**
     * مسح جميع التحميلات المكتملة
     */
    fun clearCompletedDownloads() {
        viewModelScope.launch {
            try {
                val currentDownloads = downloads.value ?: emptyList()
                val completedDownloads = currentDownloads.filter { it.status == DownloadStatus.COMPLETED }
                
                completedDownloads.forEach { download ->
                    downloadManager.deleteDownload(download.id)
                }
                
                _message.value = "تم مسح ${completedDownloads.size} تحميل مكتمل"
                updateStatistics()
                
            } catch (e: Exception) {
                _errorMessage.value = "خطأ في مسح التحميلات: ${e.message}"
            }
        }
    }
    
    /**
     * إيقاف جميع التحميلات
     */
    fun pauseAllDownloads() {
        viewModelScope.launch {
            try {
                val currentDownloads = downloads.value ?: emptyList()
                val activeDownloads = currentDownloads.filter { it.status == DownloadStatus.DOWNLOADING }
                
                activeDownloads.forEach { download ->
                    downloadManager.pauseDownload(download.id)
                }
                
                _message.value = "تم إيقاف ${activeDownloads.size} تحميل"
                
            } catch (e: Exception) {
                _errorMessage.value = "خطأ في إيقاف التحميلات: ${e.message}"
            }
        }
    }
    
    /**
     * استئناف جميع التحميلات
     */
    fun resumeAllDownloads() {
        viewModelScope.launch {
            try {
                val currentDownloads = downloads.value ?: emptyList()
                val pausedDownloads = currentDownloads.filter { it.status == DownloadStatus.PAUSED }
                
                pausedDownloads.forEach { download ->
                    downloadManager.resumeDownload(download.id)
                }
                
                _message.value = "تم استئناف ${pausedDownloads.size} تحميل"
                
            } catch (e: Exception) {
                _errorMessage.value = "خطأ في استئناف التحميلات: ${e.message}"
            }
        }
    }
    
    /**
     * تحديث الإحصائيات
     */
    private fun updateStatistics() {
        viewModelScope.launch {
            try {
                val currentDownloads = downloads.value ?: emptyList()
                
                val stats = DownloadStatistics(
                    totalDownloads = currentDownloads.size,
                    completedDownloads = currentDownloads.count { it.status == DownloadStatus.COMPLETED },
                    activeDownloads = currentDownloads.count { it.status == DownloadStatus.DOWNLOADING },
                    pausedDownloads = currentDownloads.count { it.status == DownloadStatus.PAUSED },
                    failedDownloads = currentDownloads.count { it.status == DownloadStatus.FAILED },
                    totalSize = currentDownloads.sumOf { it.totalSize },
                    downloadedSize = currentDownloads.sumOf { it.downloadedSize }
                )
                
                _downloadStats.value = stats
                
            } catch (e: Exception) {
                android.util.Log.e("AdvancedDownloadViewModel", "خطأ في تحديث الإحصائيات", e)
            }
        }
    }
    
    /**
     * إشعار تحديث مكتبة الأغاني
     */
    private fun notifyMusicLibraryUpdate() {
        val intent = android.content.Intent("com.musicplayer.pro.MUSIC_LIBRARY_UPDATED")
        getApplication<Application>().sendBroadcast(intent)
    }
    
    /**
     * التحقق من صحة الرابط
     */
    private fun isValidUrl(url: String): Boolean {
        return try {
            val urlPattern = Regex(
                "^(https?://)?" +
                "([\\w\\-]+\\.)+[\\w\\-]+" +
                "(/[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=]*)?$"
            )
            urlPattern.matches(url) && url.isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * مسح رسالة الخطأ
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
    
    /**
     * مسح الرسالة
     */
    fun clearMessage() {
        _message.value = ""
    }
    
    /**
     * الحصول على تحميل بالمعرف
     */
    fun getDownloadById(downloadId: String): Download? {
        return downloads.value?.find { it.id == downloadId }
    }
    
    /**
     * تشغيل ملف محمل
     */
    fun playDownloadedFile(downloadId: String) {
        viewModelScope.launch {
            try {
                val download = getDownloadById(downloadId)
                if (download?.status == DownloadStatus.COMPLETED) {
                    // إرسال intent لتشغيل الملف
                    val intent = android.content.Intent("com.musicplayer.pro.PLAY_DOWNLOADED_FILE").apply {
                        putExtra("file_path", download.filePath)
                        putExtra("title", download.title)
                        putExtra("artist", download.artist)
                    }
                    getApplication<Application>().sendBroadcast(intent)
                    
                    _message.value = "جاري تشغيل: ${download.title}"
                } else {
                    _errorMessage.value = "لا يمكن تشغيل الملف - التحميل غير مكتمل"
                }
            } catch (e: Exception) {
                _errorMessage.value = "خطأ في تشغيل الملف: ${e.message}"
            }
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        downloadManager.cleanup()
    }
}

/**
 * إحصائيات التحميل
 */
data class DownloadStatistics(
    val totalDownloads: Int,
    val completedDownloads: Int,
    val activeDownloads: Int,
    val pausedDownloads: Int,
    val failedDownloads: Int,
    val totalSize: Long,
    val downloadedSize: Long
) {
    val completionPercentage: Int
        get() = if (totalDownloads > 0) (completedDownloads * 100) / totalDownloads else 0
    
    val overallProgress: Int
        get() = if (totalSize > 0) ((downloadedSize * 100) / totalSize).toInt() else 0
}
