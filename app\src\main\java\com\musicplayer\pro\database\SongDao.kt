package com.musicplayer.pro.database

import androidx.lifecycle.LiveData
import androidx.room.*
import com.musicplayer.pro.models.Song
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object للأغاني
 */
@Dao
interface SongDao {
    
    /**
     * الحصول على جميع الأغاني
     */
    @Query("SELECT * FROM songs ORDER BY title ASC")
    fun getAllSongs(): Flow<List<Song>>
    
    /**
     * الحصول على جميع الأغاني كـ LiveData
     */
    @Query("SELECT * FROM songs ORDER BY title ASC")
    fun getAllSongsLiveData(): LiveData<List<Song>>
    
    /**
     * الحصول على الأغاني المحملة فقط
     */
    @Query("SELECT * FROM songs WHERE isFromDownload = 1 ORDER BY dateAdded DESC")
    fun getDownloadedSongs(): Flow<List<Song>>
    
    /**
     * البحث في الأغاني
     */
    @Query("""
        SELECT * FROM songs 
        WHERE title LIKE '%' || :query || '%' 
        OR artist LIKE '%' || :query || '%' 
        OR album LIKE '%' || :query || '%'
        ORDER BY title ASC
    """)
    fun searchSongs(query: String): Flow<List<Song>>
    
    /**
     * الحصول على أغنية بالمعرف
     */
    @Query("SELECT * FROM songs WHERE id = :id")
    suspend fun getSongById(id: Long): Song?
    
    /**
     * الحصول على أغاني ألبوم
     */
    @Query("SELECT * FROM songs WHERE album = :albumName ORDER BY track ASC, title ASC")
    fun getSongsByAlbum(albumName: String): Flow<List<Song>>
    
    /**
     * الحصول على أغاني فنان
     */
    @Query("SELECT * FROM songs WHERE artist = :artistName ORDER BY album ASC, track ASC, title ASC")
    fun getSongsByArtist(artistName: String): Flow<List<Song>>
    
    /**
     * الحصول على الأغاني المفضلة
     */
    @Query("SELECT * FROM songs WHERE isFavorite = 1 ORDER BY title ASC")
    fun getFavoriteSongs(): Flow<List<Song>>
    
    /**
     * الحصول على الأغاني الأكثر تشغيلاً
     */
    @Query("SELECT * FROM songs WHERE playCount > 0 ORDER BY playCount DESC LIMIT :limit")
    fun getMostPlayedSongs(limit: Int = 20): Flow<List<Song>>
    
    /**
     * الحصول على الأغاني المضافة حديثاً
     */
    @Query("SELECT * FROM songs ORDER BY dateAdded DESC LIMIT :limit")
    fun getRecentlyAddedSongs(limit: Int = 20): Flow<List<Song>>
    
    /**
     * إدراج أغنية جديدة
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSong(song: Song): Long
    
    /**
     * إدراج عدة أغاني
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSongs(songs: List<Song>)
    
    /**
     * تحديث أغنية
     */
    @Update
    suspend fun updateSong(song: Song)
    
    /**
     * حذف أغنية
     */
    @Delete
    suspend fun deleteSong(song: Song)
    
    /**
     * حذف أغنية بالمعرف
     */
    @Query("DELETE FROM songs WHERE id = :id")
    suspend fun deleteSongById(id: Long)
    
    /**
     * حذف جميع الأغاني
     */
    @Query("DELETE FROM songs")
    suspend fun deleteAll()
    
    /**
     * تحديث عدد مرات التشغيل
     */
    @Query("UPDATE songs SET playCount = playCount + 1, lastPlayed = :timestamp WHERE id = :id")
    suspend fun incrementPlayCount(id: Long, timestamp: Long = System.currentTimeMillis())
    
    /**
     * تحديث حالة المفضلة
     */
    @Query("UPDATE songs SET isFavorite = :isFavorite WHERE id = :id")
    suspend fun updateFavoriteStatus(id: Long, isFavorite: Boolean)
    
    /**
     * تحديث التقييم
     */
    @Query("UPDATE songs SET rating = :rating WHERE id = :id")
    suspend fun updateRating(id: Long, rating: Float)
    
    /**
     * الحصول على إحصائيات
     */
    @Query("SELECT COUNT(*) FROM songs")
    suspend fun getTotalSongsCount(): Int
    
    @Query("SELECT COUNT(*) FROM songs WHERE isFromDownload = 1")
    suspend fun getDownloadedSongsCount(): Int
    
    @Query("SELECT SUM(duration) FROM songs")
    suspend fun getTotalDuration(): Long
    
    @Query("SELECT SUM(size) FROM songs")
    suspend fun getTotalSize(): Long
}
