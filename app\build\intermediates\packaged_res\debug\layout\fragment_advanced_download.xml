<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- قسم إدخال الرابط -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="إضافة تحميل جديد"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <!-- حقل الرابط -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="أدخل رابط الأغنية"
                    app:boxStrokeColor="@color/accent_primary"
                    app:hintTextColor="@color/text_secondary">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/urlEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textUri"
                        android:maxLines="3"
                        android:textColor="@color/text_primary" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- صف الخيارات -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <!-- اختيار الجودة -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginEnd="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الجودة"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:layout_marginBottom="4dp" />

                        <Spinner
                            android:id="@+id/qualitySpinner"
                            android:layout_width="match_parent"
                            android:layout_height="48dp"
                            android:background="@drawable/spinner_background" />

                    </LinearLayout>

                    <!-- اختيار التنسيق -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:layout_marginStart="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="التنسيق"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:layout_marginBottom="4dp" />

                        <Spinner
                            android:id="@+id/formatSpinner"
                            android:layout_width="match_parent"
                            android:layout_height="48dp"
                            android:background="@drawable/spinner_background" />

                    </LinearLayout>

                </LinearLayout>

                <!-- زر التحميل -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/downloadButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="بدء التحميل"
                    android:textColor="@color/text_on_accent"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:backgroundTint="@color/accent_primary"
                    app:cornerRadius="8dp"
                    app:icon="@drawable/ic_download"
                    app:iconGravity="textStart"
                    app:iconPadding="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- شريط التقدم العام -->
        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_marginBottom="8dp"
            android:visibility="gone"
            android:progressTint="@color/accent_primary" />

        <!-- نص الحالة -->
        <TextView
            android:id="@+id/statusTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:gravity="center"
            tools:text="جاري التحميل..." />

        <!-- إحصائيات التحميل -->
        <TextView
            android:id="@+id/statsTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            android:gravity="center"
            tools:text="المجموع: 5 | مكتمل: 3 | نشط: 1 | متوقف: 1" />

        <!-- قائمة التحميلات -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/downloadsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:clipToPadding="false"
            android:paddingBottom="80dp"
            tools:listitem="@layout/item_advanced_download" />

    </LinearLayout>

    <!-- زر الإجراءات العائم -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_settings"
        app:backgroundTint="@color/accent_primary"
        app:tint="@color/text_on_accent" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
