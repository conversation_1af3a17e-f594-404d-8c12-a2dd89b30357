<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material Design 3 - Light Theme Colors -->
    
    <!-- Primary Colors -->
    <color name="md_theme_light_primary">#6750A4</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_primaryContainer">#EADDFF</color>
    <color name="md_theme_light_onPrimaryContainer">#21005D</color>
    
    <!-- Secondary Colors -->
    <color name="md_theme_light_secondary">#625B71</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_secondaryContainer">#E8DEF8</color>
    <color name="md_theme_light_onSecondaryContainer">#1D192B</color>
    
    <!-- Tertiary Colors -->
    <color name="md_theme_light_tertiary">#7D5260</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_tertiaryContainer">#FFD8E4</color>
    <color name="md_theme_light_onTertiaryContainer">#31111D</color>
    
    <!-- Error Colors -->
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    
    <!-- Background Colors -->
    <color name="md_theme_light_background">#FFFBFE</color>
    <color name="md_theme_light_onBackground">#1C1B1F</color>
    <color name="md_theme_light_surface">#FFFBFE</color>
    <color name="md_theme_light_onSurface">#1C1B1F</color>
    
    <!-- Surface Variants -->
    <color name="md_theme_light_surfaceVariant">#E7E0EC</color>
    <color name="md_theme_light_onSurfaceVariant">#49454F</color>
    <color name="md_theme_light_outline">#79747E</color>
    <color name="md_theme_light_inverseOnSurface">#F4EFF4</color>
    <color name="md_theme_light_inverseSurface">#313033</color>
    <color name="md_theme_light_inversePrimary">#D0BCFF</color>
    
    <!-- Material Design 3 - Dark Theme Colors -->
    
    <!-- Primary Colors -->
    <color name="md_theme_dark_primary">#D0BCFF</color>
    <color name="md_theme_dark_onPrimary">#381E72</color>
    <color name="md_theme_dark_primaryContainer">#4F378B</color>
    <color name="md_theme_dark_onPrimaryContainer">#EADDFF</color>
    
    <!-- Secondary Colors -->
    <color name="md_theme_dark_secondary">#CCC2DC</color>
    <color name="md_theme_dark_onSecondary">#332D41</color>
    <color name="md_theme_dark_secondaryContainer">#4A4458</color>
    <color name="md_theme_dark_onSecondaryContainer">#E8DEF8</color>
    
    <!-- Tertiary Colors -->
    <color name="md_theme_dark_tertiary">#EFB8C8</color>
    <color name="md_theme_dark_onTertiary">#492532</color>
    <color name="md_theme_dark_tertiaryContainer">#633B48</color>
    <color name="md_theme_dark_onTertiaryContainer">#FFD8E4</color>
    
    <!-- Error Colors -->
    <color name="md_theme_dark_error">#FFB4AB</color>
    <color name="md_theme_dark_errorContainer">#93000A</color>
    <color name="md_theme_dark_onError">#690005</color>
    <color name="md_theme_dark_onErrorContainer">#FFDAD6</color>
    
    <!-- Background Colors -->
    <color name="md_theme_dark_background">#1C1B1F</color>
    <color name="md_theme_dark_onBackground">#E6E1E5</color>
    <color name="md_theme_dark_surface">#1C1B1F</color>
    <color name="md_theme_dark_onSurface">#E6E1E5</color>
    
    <!-- Surface Variants -->
    <color name="md_theme_dark_surfaceVariant">#49454F</color>
    <color name="md_theme_dark_onSurfaceVariant">#CAC4D0</color>
    <color name="md_theme_dark_outline">#938F99</color>
    <color name="md_theme_dark_inverseOnSurface">#1C1B1F</color>
    <color name="md_theme_dark_inverseSurface">#E6E1E5</color>
    <color name="md_theme_dark_inversePrimary">#6750A4</color>
    
    <!-- Music Player Specific Colors -->
    
    <!-- Gradient Colors -->
    <color name="gradient_start_purple">#667eea</color>
    <color name="gradient_end_purple">#764ba2</color>
    <color name="gradient_start_blue">#4facfe</color>
    <color name="gradient_end_blue">#00f2fe</color>
    <color name="gradient_start_pink">#f093fb</color>
    <color name="gradient_end_pink">#f5576c</color>
    <color name="gradient_start_orange">#ffecd2</color>
    <color name="gradient_end_orange">#fcb69f</color>
    
    <!-- Accent Colors for Music Themes -->
    <color name="music_accent_purple">#8B5CF6</color>
    <color name="music_accent_blue">#3B82F6</color>
    <color name="music_accent_green">#10B981</color>
    <color name="music_accent_pink">#EC4899</color>
    <color name="music_accent_orange">#F59E0B</color>
    <color name="music_accent_red">#EF4444</color>
    
    <!-- Glass Effect Colors -->
    <color name="glass_surface">#80FFFFFF</color>
    <color name="glass_surface_dark">#80000000</color>
    <color name="glass_border">#40FFFFFF</color>
    <color name="glass_border_dark">#40FFFFFF</color>
    
    <!-- Semantic Colors -->
    <color name="danger">#EF4444</color>
    
    <!-- Player Controls -->
    <color name="player_control_active">#6750A4</color>
    <color name="player_control_inactive">#79747E</color>
    <color name="progress_bar_primary">#6750A4</color>
    <color name="progress_bar_secondary">#E7E0EC</color>
    
    <!-- Card Colors -->
    <color name="card_background_light">#FFFFFF</color>
    <color name="card_background_dark">#2D2D30</color>
    <color name="card_shadow">#1A000000</color>
    
    <!-- Shimmer Effect -->
    <color name="shimmer_base">#F0F0F0</color>
    <color name="shimmer_highlight">#FFFFFF</color>
    <color name="shimmer_base_dark">#2D2D30</color>
    <color name="shimmer_highlight_dark">#3D3D40</color>
</resources>
