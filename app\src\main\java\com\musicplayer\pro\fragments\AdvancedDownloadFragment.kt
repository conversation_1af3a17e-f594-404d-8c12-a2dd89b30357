package com.musicplayer.pro.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.snackbar.Snackbar
import com.musicplayer.pro.R
import com.musicplayer.pro.adapters.AdvancedDownloadAdapter
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.viewmodels.AdvancedDownloadViewModel
import kotlinx.coroutines.launch

/**
 * Fragment متقدم للتحميل مع واجهة محسنة
 */
class AdvancedDownloadFragment : Fragment() {
    
    private val viewModel: AdvancedDownloadViewModel by viewModels()
    private lateinit var adapter: AdvancedDownloadAdapter
    
    // Views
    private lateinit var urlEditText: EditText
    private lateinit var qualitySpinner: Spinner
    private lateinit var formatSpinner: Spinner
    private lateinit var downloadButton: Button
    private lateinit var recyclerView: RecyclerView
    private lateinit var progressBar: ProgressBar
    private lateinit var statusTextView: TextView
    private lateinit var statsTextView: TextView
    private lateinit var fab: FloatingActionButton
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_advanced_download, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        setupRecyclerView()
        setupSpinners()
        setupClickListeners()
        observeData()
    }
    
    /**
     * تهيئة Views
     */
    private fun initViews(view: View) {
        urlEditText = view.findViewById(R.id.urlEditText)
        qualitySpinner = view.findViewById(R.id.qualitySpinner)
        formatSpinner = view.findViewById(R.id.formatSpinner)
        downloadButton = view.findViewById(R.id.downloadButton)
        recyclerView = view.findViewById(R.id.downloadsRecyclerView)
        progressBar = view.findViewById(R.id.progressBar)
        statusTextView = view.findViewById(R.id.statusTextView)
        statsTextView = view.findViewById(R.id.statsTextView)
        fab = view.findViewById(R.id.fab)
    }
    
    /**
     * إعداد RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = AdvancedDownloadAdapter(
            onDownloadClick = { download ->
                handleDownloadClick(download)
            },
            onActionClick = { download, action ->
                handleActionClick(download, action)
            },
            onDeleteClick = { download ->
                handleDeleteClick(download)
            }
        )
        
        recyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = <EMAIL>
        }
    }
    
    /**
     * إعداد Spinners
     */
    private fun setupSpinners() {
        // إعداد spinner الجودة
        val qualityOptions = arrayOf("Best", "High", "Medium", "Low")
        val qualityAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, qualityOptions)
        qualityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        qualitySpinner.adapter = qualityAdapter
        
        // إعداد spinner التنسيق
        val formatOptions = arrayOf("MP3", "MP4", "M4A", "WEBM")
        val formatAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, formatOptions)
        formatAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        formatSpinner.adapter = formatAdapter
    }
    
    /**
     * إعداد مستمعي النقر
     */
    private fun setupClickListeners() {
        downloadButton.setOnClickListener {
            startDownload()
        }
        
        fab.setOnClickListener {
            showDownloadOptions()
        }
    }
    
    /**
     * مراقبة البيانات
     */
    private fun observeData() {
        // مراقبة التحميلات
        viewModel.downloads.observe(viewLifecycleOwner) { downloads ->
            adapter.updateDownloads(downloads)
            updateEmptyState(downloads.isEmpty())
        }
        
        // مراقبة التقدم
        viewModel.downloadProgress.observe(viewLifecycleOwner) { progressMap ->
            adapter.updateProgress(progressMap)
        }
        
        // مراقبة حالة التحميل
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            downloadButton.isEnabled = !isLoading
        }
        
        // مراقبة الرسائل
        viewModel.message.observe(viewLifecycleOwner) { message ->
            if (message.isNotEmpty()) {
                statusTextView.text = message
                showSnackbar(message)
            }
        }
        
        // مراقبة رسائل الخطأ
        viewModel.errorMessage.observe(viewLifecycleOwner) { error ->
            error?.let {
                showErrorSnackbar(it)
                viewModel.clearErrorMessage()
            }
        }
        
        // مراقبة الإحصائيات
        viewModel.downloadStats.observe(viewLifecycleOwner) { stats ->
            updateStatsDisplay(stats)
        }
    }
    
    /**
     * بدء التحميل
     */
    private fun startDownload() {
        val url = urlEditText.text.toString().trim()
        
        if (url.isEmpty()) {
            showErrorSnackbar("يرجى إدخال رابط صحيح")
            return
        }
        
        val quality = qualitySpinner.selectedItem.toString().lowercase()
        val format = formatSpinner.selectedItem.toString().lowercase()
        
        viewModel.startDownload(url, quality, format)
        
        // مسح الرابط بعد بدء التحميل
        urlEditText.text.clear()
    }
    
    /**
     * التعامل مع نقر التحميل
     */
    private fun handleDownloadClick(download: Download) {
        when (download.status) {
            com.musicplayer.pro.models.DownloadStatus.COMPLETED -> {
                // تشغيل الملف
                viewModel.playDownloadedFile(download.id)
            }
            com.musicplayer.pro.models.DownloadStatus.FAILED -> {
                // إعادة المحاولة
                showRetryDialog(download)
            }
            else -> {
                // عرض تفاصيل التحميل
                showDownloadDetails(download)
            }
        }
    }
    
    /**
     * التعامل مع نقر الإجراء
     */
    private fun handleActionClick(download: Download, action: String) {
        when (action) {
            "pause" -> viewModel.pauseDownload(download.id)
            "resume" -> viewModel.resumeDownload(download.id)
            "cancel" -> viewModel.cancelDownload(download.id)
            "play" -> viewModel.playDownloadedFile(download.id)
        }
    }
    
    /**
     * التعامل مع نقر الحذف
     */
    private fun handleDeleteClick(download: Download) {
        showDeleteConfirmationDialog(download)
    }
    
    /**
     * عرض خيارات التحميل
     */
    private fun showDownloadOptions() {
        val options = arrayOf(
            "مسح التحميلات المكتملة",
            "إيقاف جميع التحميلات",
            "استئناف جميع التحميلات",
            "تحديث القائمة"
        )
        
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("خيارات التحميل")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> viewModel.clearCompletedDownloads()
                    1 -> viewModel.pauseAllDownloads()
                    2 -> viewModel.resumeAllDownloads()
                    3 -> refreshDownloads()
                }
            }
            .show()
    }
    
    /**
     * عرض حوار إعادة المحاولة
     */
    private fun showRetryDialog(download: Download) {
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("إعادة المحاولة")
            .setMessage("هل تريد إعادة محاولة تحميل ${download.title}؟")
            .setPositiveButton("نعم") { _, _ ->
                viewModel.startDownload(download.url, download.quality, download.format)
            }
            .setNegativeButton("لا", null)
            .show()
    }
    
    /**
     * عرض حوار تأكيد الحذف
     */
    private fun showDeleteConfirmationDialog(download: Download) {
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("حذف التحميل")
            .setMessage("هل تريد حذف ${download.title}؟")
            .setPositiveButton("حذف") { _, _ ->
                viewModel.deleteDownload(download.id)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    /**
     * عرض تفاصيل التحميل
     */
    private fun showDownloadDetails(download: Download) {
        val details = """
            العنوان: ${download.title}
            الفنان: ${download.artist}
            الحالة: ${getStatusText(download.status)}
            التقدم: ${download.getProgressPercentage()}%
            الحجم: ${download.getFormattedDownloadedSize()} / ${download.getFormattedTotalSize()}
            السرعة: ${download.speed}
            الوقت المتبقي: ${download.eta}
        """.trimIndent()
        
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("تفاصيل التحميل")
            .setMessage(details)
            .setPositiveButton("موافق", null)
            .show()
    }
    
    /**
     * تحديث عرض الحالة الفارغة
     */
    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            statusTextView.text = "لا توجد تحميلات"
            statsTextView.text = ""
        }
    }
    
    /**
     * تحديث عرض الإحصائيات
     */
    private fun updateStatsDisplay(stats: com.musicplayer.pro.viewmodels.DownloadStatistics) {
        val statsText = """
            المجموع: ${stats.totalDownloads} | 
            مكتمل: ${stats.completedDownloads} | 
            نشط: ${stats.activeDownloads} | 
            متوقف: ${stats.pausedDownloads} | 
            فاشل: ${stats.failedDownloads}
        """.trimIndent()
        
        statsTextView.text = statsText
    }
    
    /**
     * تحديث التحميلات
     */
    private fun refreshDownloads() {
        // يمكن إضافة منطق تحديث إضافي هنا
        showSnackbar("تم تحديث قائمة التحميلات")
    }
    
    /**
     * عرض Snackbar
     */
    private fun showSnackbar(message: String) {
        Snackbar.make(requireView(), message, Snackbar.LENGTH_SHORT).show()
    }
    
    /**
     * عرض Snackbar للخطأ
     */
    private fun showErrorSnackbar(message: String) {
        Snackbar.make(requireView(), message, Snackbar.LENGTH_LONG)
            .setBackgroundTint(resources.getColor(android.R.color.holo_red_dark, null))
            .show()
    }
    
    /**
     * الحصول على نص الحالة
     */
    private fun getStatusText(status: com.musicplayer.pro.models.DownloadStatus): String {
        return when (status) {
            com.musicplayer.pro.models.DownloadStatus.PENDING -> "في الانتظار"
            com.musicplayer.pro.models.DownloadStatus.DOWNLOADING -> "جاري التحميل"
            com.musicplayer.pro.models.DownloadStatus.PAUSED -> "متوقف"
            com.musicplayer.pro.models.DownloadStatus.COMPLETED -> "مكتمل"
            com.musicplayer.pro.models.DownloadStatus.FAILED -> "فاشل"
            com.musicplayer.pro.models.DownloadStatus.CANCELLED -> "ملغي"
        }
    }
}
