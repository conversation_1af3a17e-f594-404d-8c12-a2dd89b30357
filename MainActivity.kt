package com.musicplayer.pro

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Environment
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.musicplayer.pro.models.Song
import com.musicplayer.pro.fragments.MainFragment
import com.musicplayer.pro.fragments.NowPlayingFragment
import com.musicplayer.pro.fragments.SimpleDownloadFragment
import com.musicplayer.pro.fragments.LoadingFragment
import com.musicplayer.pro.services.MusicService
import com.musicplayer.pro.utils.PermissionManager
import com.musicplayer.pro.utils.ThemeManager
import com.musicplayer.pro.utils.AudioManager
import com.musicplayer.pro.utils.PreferenceManager

/**
 * النشاط الرئيسي للتطبيق
 * يدير التنقل بين الشاشات المختلفة ويتعامل مع الأذونات
 */
class MainActivity : AppCompatActivity() {
    
    companion object {
        private const val REQUEST_PERMISSIONS = 1001
        private const val TAG = "MainActivity"
    }
    
    // UI Components
    private lateinit var viewPager: ViewPager2
    private lateinit var tabLayout: TabLayout
    
    // Managers
    private lateinit var permissionManager: PermissionManager
    private lateinit var themeManager: ThemeManager
    private lateinit var audioManager: AudioManager
    private lateinit var preferenceManager: PreferenceManager
    
    // Service
    private var musicService: MusicService? = null
    private var isServiceBound = false
    
    // State
    private var isLoadingComplete = false

    // مستمع تحديث مكتبة الأغاني
    private val musicLibraryUpdateReceiver = object : android.content.BroadcastReceiver() {
        override fun onReceive(context: android.content.Context?, intent: android.content.Intent?) {
            if (intent?.action == "com.musicplayer.pro.MUSIC_LIBRARY_UPDATED") {
                android.util.Log.d("MainActivity", "تم استلام إشعار تحديث مكتبة الأغاني")
                // تحديث الشاشة الرئيسية
                refreshMainFragment()
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // تهيئة المدراء
        initializeManagers()
        
        // تطبيق الثيم
        themeManager.applyTheme()
        
        setContentView(R.layout.activity_main)
        
        // تهيئة UI
        initializeUI()
        
        // طلب الأذونات
        requestNecessaryPermissions()
        
        // بدء خدمة الموسيقى
        startMusicService()
        
        // تحميل البيانات
        startLoadingProcess()

        // تسجيل مستمع تحديث مكتبة الأغاني
        registerMusicLibraryUpdateReceiver()

        // إعداد النظام المتقدم
        setupAdvancedSystems()
    }
    
    /**
     * تهيئة المدراء المختلفة
     */
    private fun initializeManagers() {
        permissionManager = PermissionManager(this)
        themeManager = ThemeManager(this)
        audioManager = AudioManager(this)
        preferenceManager = PreferenceManager(this)
    }
    
    /**
     * تهيئة واجهة المستخدم
     */
    private fun initializeUI() {
        viewPager = findViewById(R.id.viewPager)
        tabLayout = findViewById(R.id.tabLayout)
        
        setupViewPager()
        setupTabLayout()
    }
    
    /**
     * إعداد ViewPager مع الشاشات المختلفة
     */
    private fun setupViewPager() {
        val adapter = ScreenPagerAdapter(this)
        viewPager.adapter = adapter
        
        // تعطيل التمرير إذا كان التحميل لم يكتمل
        viewPager.isUserInputEnabled = isLoadingComplete
    }
    
    /**
     * إعداد TabLayout
     */
    private fun setupTabLayout() {
        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> if (isLoadingComplete) getString(R.string.main_screen) else getString(R.string.loading)
                1 -> getString(R.string.now_playing)
                2 -> getString(R.string.downloads)
                else -> ""
            }
            
            // إضافة أيقونات للتابات
            tab.setIcon(when (position) {
                0 -> if (isLoadingComplete) R.drawable.ic_music_library else R.drawable.ic_loading
                1 -> R.drawable.ic_now_playing
                2 -> R.drawable.ic_download
                else -> null
            })
        }.attach()
    }
    
    /**
     * طلب الأذونات الضرورية
     */
    private fun requestNecessaryPermissions() {
        val permissions = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.INTERNET,
            Manifest.permission.ACCESS_NETWORK_STATE,
            Manifest.permission.WAKE_LOCK,
            Manifest.permission.FOREGROUND_SERVICE,
            Manifest.permission.MODIFY_AUDIO_SETTINGS,
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.VIBRATE
        )
        
        permissionManager.requestPermissions(permissions) { granted ->
            if (granted) {
                onPermissionsGranted()
            } else {
                onPermissionsDenied()
            }
        }
    }
    
    /**
     * عند منح الأذونات
     */
    private fun onPermissionsGranted() {
        // بدء مسح الملفات الصوتية
        audioManager.startAudioScan()
    }
    
    /**
     * عند رفض الأذونات
     */
    private fun onPermissionsDenied() {
        // عرض رسالة للمستخدم
        showPermissionDeniedDialog()
    }
    
    /**
     * عرض حوار رفض الأذونات
     */
    private fun showPermissionDeniedDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(getString(R.string.permissions_required))
            .setMessage(getString(R.string.permissions_required_message))
            .setPositiveButton(getString(R.string.grant_permissions)) { _, _ ->
                requestNecessaryPermissions()
            }
            .setNegativeButton(getString(R.string.exit)) { _, _ ->
                finish()
            }
            .setCancelable(false)
            .show()
    }
    
    /**
     * بدء خدمة الموسيقى
     */
    private fun startMusicService() {
        val serviceIntent = Intent(this, MusicService::class.java)
        startForegroundService(serviceIntent)
        bindService(serviceIntent, musicServiceConnection, BIND_AUTO_CREATE)
    }
    
    /**
     * اتصال خدمة الموسيقى
     */
    private val musicServiceConnection = object : android.content.ServiceConnection {
        override fun onServiceConnected(name: android.content.ComponentName?, service: android.os.IBinder?) {
            val binder = service as MusicService.MusicBinder
            musicService = binder.getService()
            isServiceBound = true
            
            // إعداد المستمعين
            setupServiceListeners()
        }
        
        override fun onServiceDisconnected(name: android.content.ComponentName?) {
            musicService = null
            isServiceBound = false
        }
    }
    
    /**
     * إعداد مستمعي الخدمة
     */
    private fun setupServiceListeners() {
        musicService?.setOnPlaybackStateChangedListener { isPlaying ->
            // تحديث UI حسب حالة التشغيل
            updatePlaybackUI(isPlaying)
        }
        
        musicService?.setOnSongChangedListener { song ->
            // تحديث UI عند تغيير الأغنية
            updateCurrentSongUI(song)
        }
    }
    
    /**
     * تحديث UI حسب حالة التشغيل
     */
    private fun updatePlaybackUI(isPlaying: Boolean) {
        // تحديث أيقونة التشغيل في التاب
        val nowPlayingTab = tabLayout.getTabAt(1)
        nowPlayingTab?.setIcon(
            if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play
        )
    }
    
    /**
     * تحديث UI عند تغيير الأغنية
     */
    private fun updateCurrentSongUI(song: Song?) {
        // تحديث عنوان التاب بعنوان الأغنية
        if (song != null) {
            val nowPlayingTab = tabLayout.getTabAt(1)
            nowPlayingTab?.text = song.title
        }
    }
    
    /**
     * بدء عملية التحميل
     */
    private fun startLoadingProcess() {
        // عرض شاشة التحميل
        viewPager.currentItem = 0
        
        // محاكاة عملية التحميل
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            completeLoading()
        }, 3000) // 3 ثوانٍ للتحميل
    }
    
    /**
     * إكمال عملية التحميل
     */
    private fun completeLoading() {
        isLoadingComplete = true
        
        // تمكين التمرير
        viewPager.isUserInputEnabled = true
        
        // تحديث التابات
        setupTabLayout()
        
        // الانتقال للشاشة الرئيسية
        viewPager.currentItem = 0
    }
    
    /**
     * محول الشاشات
     */
    private inner class ScreenPagerAdapter(activity: AppCompatActivity) : FragmentStateAdapter(activity) {
        
        override fun getItemCount(): Int = 3
        
        override fun createFragment(position: Int): Fragment {
            return when (position) {
                0 -> if (isLoadingComplete) MainFragment() else LoadingFragment()
                1 -> NowPlayingFragment()
                2 -> SimpleDownloadFragment()
                else -> MainFragment()
            }
        }
    }
    
    /**
     * تسجيل مستمع تحديث مكتبة الأغاني
     */
    private fun registerMusicLibraryUpdateReceiver() {
        try {
            val filter = android.content.IntentFilter("com.musicplayer.pro.MUSIC_LIBRARY_UPDATED")
            registerReceiver(musicLibraryUpdateReceiver, filter)
            android.util.Log.d("MainActivity", "تم تسجيل مستمع تحديث مكتبة الأغاني")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في تسجيل مستمع تحديث مكتبة الأغاني: ${e.message}")
        }
    }

    /**
     * إلغاء تسجيل مستمع تحديث مكتبة الأغاني
     */
    private fun unregisterMusicLibraryUpdateReceiver() {
        try {
            unregisterReceiver(musicLibraryUpdateReceiver)
            android.util.Log.d("MainActivity", "تم إلغاء تسجيل مستمع تحديث مكتبة الأغاني")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في إلغاء تسجيل مستمع تحديث مكتبة الأغاني: ${e.message}")
        }
    }

    /**
     * إعداد النظام المتقدم
     */
    private fun setupAdvancedSystems() {
        try {
            android.util.Log.d("MainActivity", "إعداد النظام المتقدم للتحميل والموسيقى")

            // إعداد قاعدة البيانات المحلية
            // سيتم تهيئتها تلقائياً عند الحاجة

            // إعداد مستمعين إضافيين للنظام الجديد
            setupAdvancedBroadcastReceivers()

        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في إعداد النظام المتقدم: ${e.message}")
        }
    }

    /**
     * إعداد مستمعين متقدمين
     */
    private fun setupAdvancedBroadcastReceivers() {
        try {
            val filter = android.content.IntentFilter().apply {
                addAction("com.musicplayer.pro.DOWNLOAD_COMPLETED")
                addAction("com.musicplayer.pro.PLAY_DOWNLOADED_FILE")
                addAction("com.musicplayer.pro.SIMPLE_DOWNLOAD_COMPLETED")
                addAction("com.musicplayer.pro.PLAY_SIMPLE_DOWNLOAD")
            }

            val advancedReceiver = object : android.content.BroadcastReceiver() {
                override fun onReceive(context: android.content.Context?, intent: android.content.Intent?) {
                    when (intent?.action) {
                        "com.musicplayer.pro.DOWNLOAD_COMPLETED" -> {
                            val title = intent.getStringExtra("title") ?: "أغنية"
                            showDownloadCompletedNotification(title)
                        }
                        "com.musicplayer.pro.PLAY_DOWNLOADED_FILE" -> {
                            val filePath = intent.getStringExtra("file_path")
                            val title = intent.getStringExtra("title")
                            val artist = intent.getStringExtra("artist")
                            playDownloadedFile(filePath, title, artist)
                        }
                        "com.musicplayer.pro.SIMPLE_DOWNLOAD_COMPLETED" -> {
                            val title = intent.getStringExtra("title") ?: "أغنية"
                            showDownloadCompletedNotification(title)
                            android.util.Log.d("MainActivity", "تم اكتمال التحميل البسيط: $title")
                        }
                        "com.musicplayer.pro.PLAY_SIMPLE_DOWNLOAD" -> {
                            val filePath = intent.getStringExtra("file_path")
                            val title = intent.getStringExtra("title")
                            val artist = intent.getStringExtra("artist")
                            playSimpleDownload(filePath, title, artist)
                        }
                    }
                }
            }

            registerReceiver(advancedReceiver, filter)
            android.util.Log.d("MainActivity", "تم تسجيل المستمعين المتقدمين")

        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في إعداد المستمعين المتقدمين: ${e.message}")
        }
    }

    /**
     * عرض إشعار اكتمال التحميل
     */
    private fun showDownloadCompletedNotification(title: String) {
        try {
            // يمكن إضافة إشعار نظام هنا
            android.util.Log.d("MainActivity", "تم اكتمال تحميل: $title")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في عرض إشعار التحميل: ${e.message}")
        }
    }

    /**
     * تشغيل ملف محمل
     */
    private fun playDownloadedFile(filePath: String?, title: String?, artist: String?) {
        try {
            if (filePath != null && title != null) {
                android.util.Log.d("MainActivity", "تشغيل ملف محمل: $title من المسار: $filePath")

                // التحقق من وجود الملف
                val file = java.io.File(filePath)
                if (file.exists()) {
                    // إنشاء أغنية مؤقتة للتشغيل
                    val song = com.musicplayer.pro.models.Song(
                        title = title,
                        artist = artist ?: "فنان غير معروف",
                        album = "الأغاني المحملة",
                        path = filePath,
                        duration = 0L, // سيتم تحديدها لاحقاً
                        isFromDownload = true
                    )

                    // تشغيل الأغنية
                    playSong(song)

                    android.util.Log.d("MainActivity", "تم بدء تشغيل الملف المحمل بنجاح")
                } else {
                    android.util.Log.e("MainActivity", "الملف غير موجود: $filePath")
                    showToast("الملف غير موجود")
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في تشغيل الملف المحمل: ${e.message}")
            showToast("خطأ في تشغيل الملف")
        }
    }

    /**
     * تشغيل تحميل بسيط
     */
    private fun playSimpleDownload(filePath: String?, title: String?, artist: String?) {
        try {
            if (filePath != null && title != null) {
                android.util.Log.d("MainActivity", "تشغيل تحميل بسيط: $title من المسار: $filePath")

                // التحقق من وجود الملف
                val file = java.io.File(filePath)
                if (file.exists()) {
                    // إنشاء أغنية للتشغيل
                    val song = com.musicplayer.pro.models.Song(
                        title = title,
                        artist = artist ?: "فنان غير معروف",
                        album = "الأغاني المحملة",
                        path = filePath,
                        duration = 180000L, // 3 دقائق
                        size = file.length(),
                        mimeType = "audio/mpeg",
                        isFromDownload = true
                    )

                    // تشغيل الأغنية مباشرة
                    playSong(song)

                    android.util.Log.d("MainActivity", "تم بدء تشغيل التحميل البسيط بنجاح")
                    showToast("جاري تشغيل: $title")
                } else {
                    android.util.Log.e("MainActivity", "الملف غير موجود: $filePath")
                    showToast("الملف غير موجود")
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في تشغيل التحميل البسيط: ${e.message}")
            showToast("خطأ في تشغيل الملف")
        }
    }

    /**
     * عرض رسالة Toast
     */
    private fun showToast(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    /**
     * تحديث الشاشة الرئيسية
     */
    private fun refreshMainFragment() {
        try {
            // الحصول على الشاشة الرئيسية الحالية
            val currentFragment = supportFragmentManager.fragments.find {
                it is MainFragment && it.isVisible
            } as? MainFragment

            currentFragment?.let {
                // تحديث البيانات في الشاشة الرئيسية
                android.util.Log.d("MainActivity", "تحديث الشاشة الرئيسية")
                // يمكن إضافة منطق تحديث إضافي هنا
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "خطأ في تحديث الشاشة الرئيسية: ${e.message}")
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // إلغاء تسجيل مستمع تحديث مكتبة الأغاني
        unregisterMusicLibraryUpdateReceiver()

        // إلغاء ربط الخدمة
        if (isServiceBound) {
            unbindService(musicServiceConnection)
            isServiceBound = false
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        if (requestCode == REQUEST_PERMISSIONS) {
            val allGranted = grantResults.all { it == PackageManager.PERMISSION_GRANTED }
            
            if (allGranted) {
                onPermissionsGranted()
            } else {
                onPermissionsDenied()
            }
        }
    }
}
