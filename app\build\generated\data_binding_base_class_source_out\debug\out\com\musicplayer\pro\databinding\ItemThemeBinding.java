// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemThemeBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView selectedIndicator;

  @NonNull
  public final MaterialCardView themeCard;

  @NonNull
  public final View themeColorPreview;

  @NonNull
  public final TextView themeDescription;

  @NonNull
  public final TextView themeName;

  private ItemThemeBinding(@NonNull MaterialCardView rootView, @NonNull ImageView selectedIndicator,
      @NonNull MaterialCardView themeCard, @NonNull View themeColorPreview,
      @NonNull TextView themeDescription, @NonNull TextView themeName) {
    this.rootView = rootView;
    this.selectedIndicator = selectedIndicator;
    this.themeCard = themeCard;
    this.themeColorPreview = themeColorPreview;
    this.themeDescription = themeDescription;
    this.themeName = themeName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemThemeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemThemeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_theme, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemThemeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.selectedIndicator;
      ImageView selectedIndicator = ViewBindings.findChildViewById(rootView, id);
      if (selectedIndicator == null) {
        break missingId;
      }

      MaterialCardView themeCard = (MaterialCardView) rootView;

      id = R.id.themeColorPreview;
      View themeColorPreview = ViewBindings.findChildViewById(rootView, id);
      if (themeColorPreview == null) {
        break missingId;
      }

      id = R.id.themeDescription;
      TextView themeDescription = ViewBindings.findChildViewById(rootView, id);
      if (themeDescription == null) {
        break missingId;
      }

      id = R.id.themeName;
      TextView themeName = ViewBindings.findChildViewById(rootView, id);
      if (themeName == null) {
        break missingId;
      }

      return new ItemThemeBinding((MaterialCardView) rootView, selectedIndicator, themeCard,
          themeColorPreview, themeDescription, themeName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
