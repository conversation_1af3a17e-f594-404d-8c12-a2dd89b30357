<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- مؤشر الحالة -->
        <View
            android:id="@+id/statusIndicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="12dp"
            android:background="@color/accent_primary" />

        <!-- الصورة المصغرة -->
        <ImageView
            android:id="@+id/downloadThumbnail"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/bg_thumbnail"
            android:padding="8dp"
            android:scaleType="centerCrop"
            android:src="@drawable/default_album_cover" />

        <!-- المحتوى الرئيسي -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- الصف الأول: العنوان والأزرار -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <!-- العنوان -->
                    <TextView
                        android:id="@+id/downloadTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:maxLines="1"
                        android:ellipsize="end"
                        tools:text="اسم الأغنية الطويل جداً" />

                    <!-- الفنان -->
                    <TextView
                        android:id="@+id/downloadArtist"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        tools:text="اسم الفنان" />

                </LinearLayout>

                <!-- أزرار الإجراءات -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageButton
                        android:id="@+id/actionButton"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginEnd="4dp"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:src="@drawable/ic_play"
                        android:contentDescription="إجراء"
                        app:tint="@color/accent_primary" />

                    <ImageButton
                        android:id="@+id/deleteButton"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:src="@drawable/ic_delete"
                        android:contentDescription="حذف"
                        app:tint="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

            <!-- الصف الثاني: الحالة والجودة -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/downloadStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/accent_primary"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    tools:text="جاري التحميل" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/downloadQuality"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_quality_badge"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp"
                    android:textColor="@color/text_on_accent"
                    android:textSize="10sp"
                    android:textStyle="bold"
                    tools:text="MP3 • BEST • 3:45" />

            </LinearLayout>

            <!-- شريط التقدم -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ProgressBar
                    android:id="@+id/downloadProgress"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="0dp"
                    android:layout_height="6dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:progressTint="@color/accent_primary"
                    android:progressBackgroundTint="@color/background_secondary"
                    tools:progress="65" />

                <TextView
                    android:id="@+id/progressText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    tools:text="65%" />

            </LinearLayout>

            <!-- الصف الأخير: معلومات التحميل -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <!-- الحجم -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الحجم"
                        android:textColor="@color/text_secondary"
                        android:textSize="10sp" />

                    <TextView
                        android:id="@+id/downloadSize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_primary"
                        android:textSize="12sp"
                        tools:text="2.5 / 4.2 MB" />

                </LinearLayout>

                <!-- السرعة -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="السرعة"
                        android:textColor="@color/text_secondary"
                        android:textSize="10sp" />

                    <TextView
                        android:id="@+id/downloadSpeed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_primary"
                        android:textSize="12sp"
                        tools:text="1.2 MB/s" />

                </LinearLayout>

                <!-- الوقت المتبقي -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="المتبقي"
                        android:textColor="@color/text_secondary"
                        android:textSize="10sp" />

                    <TextView
                        android:id="@+id/downloadEta"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_primary"
                        android:textSize="12sp"
                        tools:text="1m 30s" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
