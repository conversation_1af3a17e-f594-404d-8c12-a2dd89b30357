package com.musicplayer.pro.themes

import android.content.Context
import android.content.SharedPreferences
import androidx.appcompat.app.AppCompatActivity
import com.musicplayer.pro.R

/**
 * مدير الثيمات المتقدم - نظام ثيمات شامل وحديث
 */
class ThemeManager(private val context: Context) {
    
    companion object {
        private const val PREFS_NAME = "theme_preferences"
        private const val KEY_CURRENT_THEME = "current_theme"
        private const val KEY_DYNAMIC_COLORS = "dynamic_colors"
        private const val KEY_FOLLOW_SYSTEM = "follow_system"
        
        // Theme IDs
        const val THEME_MODERN = "modern"
        const val THEME_PURPLE = "purple"
        const val THEME_BLUE = "blue"
        const val THEME_GREEN = "green"
        const val THEME_PINK = "pink"
        const val THEME_ORANGE = "orange"
        const val THEME_DARK = "dark"
        const val THEME_AMOLED = "amoled"
        
        @Volatile
        private var INSTANCE: ThemeManager? = null
        
        fun getInstance(context: Context): ThemeManager {
            return INSTANCE ?: synchronized(this) {
                val instance = ThemeManager(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    /**
     * الحصول على الثيم الحالي
     */
    fun getCurrentTheme(): String {
        return prefs.getString(KEY_CURRENT_THEME, THEME_MODERN) ?: THEME_MODERN
    }
    
    /**
     * تطبيق ثيم جديد
     */
    fun applyTheme(activity: AppCompatActivity, themeId: String) {
        // حفظ الثيم المختار
        prefs.edit().putString(KEY_CURRENT_THEME, themeId).apply()
        
        // تطبيق الثيم
        val themeResId = getThemeResourceId(themeId)
        activity.setTheme(themeResId)
        
        android.util.Log.d("ThemeManager", "تم تطبيق الثيم: $themeId")
    }
    
    /**
     * الحصول على معرف مورد الثيم
     */
    private fun getThemeResourceId(themeId: String): Int {
        return when (themeId) {
            THEME_MODERN -> R.style.Theme_MusicPlayer_Modern
            THEME_PURPLE -> R.style.Theme_MusicPlayer_Purple
            THEME_BLUE -> R.style.Theme_MusicPlayer_Blue
            THEME_GREEN -> R.style.Theme_MusicPlayer_Green
            THEME_PINK -> R.style.Theme_MusicPlayer_Pink
            THEME_ORANGE -> R.style.Theme_MusicPlayer_Orange
            THEME_DARK -> R.style.Theme_MusicPlayer_Modern // سيستخدم النسخة المظلمة تلقائياً
            THEME_AMOLED -> R.style.Theme_MusicPlayer_AMOLED
            else -> R.style.Theme_MusicPlayer_Modern
        }
    }
    
    /**
     * الحصول على جميع الثيمات المتاحة
     */
    fun getAvailableThemes(): List<ThemeInfo> {
        return listOf(
            ThemeInfo(
                id = THEME_MODERN,
                name = "حديث",
                description = "تصميم Material Design 3 الحديث",
                primaryColor = "#6750A4",
                previewImage = R.drawable.theme_preview_modern
            ),
            ThemeInfo(
                id = THEME_PURPLE,
                name = "بنفسجي",
                description = "ثيم بنفسجي أنيق ومريح للعين",
                primaryColor = "#8B5CF6",
                previewImage = R.drawable.theme_preview_purple
            ),
            ThemeInfo(
                id = THEME_BLUE,
                name = "أزرق",
                description = "ثيم أزرق هادئ ومنعش",
                primaryColor = "#3B82F6",
                previewImage = R.drawable.theme_preview_blue
            ),
            ThemeInfo(
                id = THEME_GREEN,
                name = "أخضر",
                description = "ثيم أخضر طبيعي ومريح",
                primaryColor = "#10B981",
                previewImage = R.drawable.theme_preview_green
            ),
            ThemeInfo(
                id = THEME_PINK,
                name = "وردي",
                description = "ثيم وردي جميل وحيوي",
                primaryColor = "#EC4899",
                previewImage = R.drawable.theme_preview_pink
            ),
            ThemeInfo(
                id = THEME_ORANGE,
                name = "برتقالي",
                description = "ثيم برتقالي دافئ ومشرق",
                primaryColor = "#F59E0B",
                previewImage = R.drawable.theme_preview_orange
            ),
            ThemeInfo(
                id = THEME_AMOLED,
                name = "أسود نقي",
                description = "ثيم أسود نقي لشاشات AMOLED",
                primaryColor = "#FFFFFF",
                previewImage = R.drawable.theme_preview_amoled
            )
        )
    }
    
    /**
     * تفعيل/إلغاء الألوان الديناميكية
     */
    fun setDynamicColors(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_DYNAMIC_COLORS, enabled).apply()
    }
    
    /**
     * التحقق من تفعيل الألوان الديناميكية
     */
    fun isDynamicColorsEnabled(): Boolean {
        return prefs.getBoolean(KEY_DYNAMIC_COLORS, false)
    }
    
    /**
     * تفعيل/إلغاء متابعة النظام
     */
    fun setFollowSystem(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_FOLLOW_SYSTEM, enabled).apply()
    }
    
    /**
     * التحقق من متابعة النظام
     */
    fun isFollowSystemEnabled(): Boolean {
        return prefs.getBoolean(KEY_FOLLOW_SYSTEM, true)
    }
    
    /**
     * الحصول على لون الثيم الأساسي
     */
    fun getPrimaryColor(themeId: String): String {
        return getAvailableThemes().find { it.id == themeId }?.primaryColor ?: "#6750A4"
    }
    
    /**
     * التحقق من كون الثيم مظلم
     */
    fun isDarkTheme(themeId: String): Boolean {
        return themeId == THEME_DARK || themeId == THEME_AMOLED
    }
    
    /**
     * تطبيق الثيم على النشاط
     */
    fun applyCurrentTheme(activity: AppCompatActivity) {
        val currentTheme = getCurrentTheme()
        applyTheme(activity, currentTheme)
    }
    
    /**
     * إعادة تشغيل النشاط مع الثيم الجديد
     */
    fun recreateWithTheme(activity: AppCompatActivity, themeId: String) {
        applyTheme(activity, themeId)
        activity.recreate()
    }
    
    /**
     * الحصول على معلومات الثيم الحالي
     */
    fun getCurrentThemeInfo(): ThemeInfo {
        val currentTheme = getCurrentTheme()
        return getAvailableThemes().find { it.id == currentTheme } 
            ?: getAvailableThemes().first()
    }
    
    /**
     * تصدير إعدادات الثيم
     */
    fun exportThemeSettings(): Map<String, Any> {
        return mapOf(
            "current_theme" to getCurrentTheme(),
            "dynamic_colors" to isDynamicColorsEnabled(),
            "follow_system" to isFollowSystemEnabled()
        )
    }
    
    /**
     * استيراد إعدادات الثيم
     */
    fun importThemeSettings(settings: Map<String, Any>) {
        val editor = prefs.edit()
        
        settings["current_theme"]?.let { 
            editor.putString(KEY_CURRENT_THEME, it as String)
        }
        
        settings["dynamic_colors"]?.let { 
            editor.putBoolean(KEY_DYNAMIC_COLORS, it as Boolean)
        }
        
        settings["follow_system"]?.let { 
            editor.putBoolean(KEY_FOLLOW_SYSTEM, it as Boolean)
        }
        
        editor.apply()
    }
}

/**
 * معلومات الثيم
 */
data class ThemeInfo(
    val id: String,
    val name: String,
    val description: String,
    val primaryColor: String,
    val previewImage: Int
)

/**
 * مستمع تغيير الثيم
 */
interface ThemeChangeListener {
    fun onThemeChanged(themeId: String)
}
