// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAdvancedDownloadBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton actionButton;

  @NonNull
  public final ImageButton deleteButton;

  @NonNull
  public final TextView downloadArtist;

  @NonNull
  public final TextView downloadEta;

  @NonNull
  public final ProgressBar downloadProgress;

  @NonNull
  public final TextView downloadQuality;

  @NonNull
  public final TextView downloadSize;

  @NonNull
  public final TextView downloadSpeed;

  @NonNull
  public final TextView downloadStatus;

  @NonNull
  public final ImageView downloadThumbnail;

  @NonNull
  public final TextView downloadTitle;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final View statusIndicator;

  private ItemAdvancedDownloadBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton actionButton, @NonNull ImageButton deleteButton,
      @NonNull TextView downloadArtist, @NonNull TextView downloadEta,
      @NonNull ProgressBar downloadProgress, @NonNull TextView downloadQuality,
      @NonNull TextView downloadSize, @NonNull TextView downloadSpeed,
      @NonNull TextView downloadStatus, @NonNull ImageView downloadThumbnail,
      @NonNull TextView downloadTitle, @NonNull TextView progressText,
      @NonNull View statusIndicator) {
    this.rootView = rootView;
    this.actionButton = actionButton;
    this.deleteButton = deleteButton;
    this.downloadArtist = downloadArtist;
    this.downloadEta = downloadEta;
    this.downloadProgress = downloadProgress;
    this.downloadQuality = downloadQuality;
    this.downloadSize = downloadSize;
    this.downloadSpeed = downloadSpeed;
    this.downloadStatus = downloadStatus;
    this.downloadThumbnail = downloadThumbnail;
    this.downloadTitle = downloadTitle;
    this.progressText = progressText;
    this.statusIndicator = statusIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAdvancedDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAdvancedDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_advanced_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAdvancedDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.actionButton;
      ImageButton actionButton = ViewBindings.findChildViewById(rootView, id);
      if (actionButton == null) {
        break missingId;
      }

      id = R.id.deleteButton;
      ImageButton deleteButton = ViewBindings.findChildViewById(rootView, id);
      if (deleteButton == null) {
        break missingId;
      }

      id = R.id.downloadArtist;
      TextView downloadArtist = ViewBindings.findChildViewById(rootView, id);
      if (downloadArtist == null) {
        break missingId;
      }

      id = R.id.downloadEta;
      TextView downloadEta = ViewBindings.findChildViewById(rootView, id);
      if (downloadEta == null) {
        break missingId;
      }

      id = R.id.downloadProgress;
      ProgressBar downloadProgress = ViewBindings.findChildViewById(rootView, id);
      if (downloadProgress == null) {
        break missingId;
      }

      id = R.id.downloadQuality;
      TextView downloadQuality = ViewBindings.findChildViewById(rootView, id);
      if (downloadQuality == null) {
        break missingId;
      }

      id = R.id.downloadSize;
      TextView downloadSize = ViewBindings.findChildViewById(rootView, id);
      if (downloadSize == null) {
        break missingId;
      }

      id = R.id.downloadSpeed;
      TextView downloadSpeed = ViewBindings.findChildViewById(rootView, id);
      if (downloadSpeed == null) {
        break missingId;
      }

      id = R.id.downloadStatus;
      TextView downloadStatus = ViewBindings.findChildViewById(rootView, id);
      if (downloadStatus == null) {
        break missingId;
      }

      id = R.id.downloadThumbnail;
      ImageView downloadThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (downloadThumbnail == null) {
        break missingId;
      }

      id = R.id.downloadTitle;
      TextView downloadTitle = ViewBindings.findChildViewById(rootView, id);
      if (downloadTitle == null) {
        break missingId;
      }

      id = R.id.progressText;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.statusIndicator;
      View statusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (statusIndicator == null) {
        break missingId;
      }

      return new ItemAdvancedDownloadBinding((MaterialCardView) rootView, actionButton,
          deleteButton, downloadArtist, downloadEta, downloadProgress, downloadQuality,
          downloadSize, downloadSpeed, downloadStatus, downloadThumbnail, downloadTitle,
          progressText, statusIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
