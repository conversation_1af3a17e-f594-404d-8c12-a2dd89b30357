<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_advanced_download" modulePackage="com.musicplayer.pro" filePath="app\src\main\res\layout\item_advanced_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_advanced_download_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="263" endOffset="51"/></Target><Target id="@+id/statusIndicator" view="View"><Expressions/><location startLine="19" startOffset="8" endLine="24" endOffset="56"/></Target><Target id="@+id/downloadThumbnail" view="ImageView"><Expressions/><location startLine="27" startOffset="8" endLine="35" endOffset="57"/></Target><Target id="@+id/downloadTitle" view="TextView"><Expressions/><location startLine="58" startOffset="20" endLine="67" endOffset="62"/></Target><Target id="@+id/downloadArtist" view="TextView"><Expressions/><location startLine="70" startOffset="20" endLine="79" endOffset="49"/></Target><Target id="@+id/actionButton" view="ImageButton"><Expressions/><location startLine="89" startOffset="20" endLine="97" endOffset="58"/></Target><Target id="@+id/deleteButton" view="ImageButton"><Expressions/><location startLine="99" startOffset="20" endLine="106" endOffset="58"/></Target><Target id="@+id/downloadStatus" view="TextView"><Expressions/><location startLine="120" startOffset="16" endLine="127" endOffset="47"/></Target><Target id="@+id/downloadQuality" view="TextView"><Expressions/><location startLine="134" startOffset="16" endLine="144" endOffset="52"/></Target><Target id="@+id/downloadProgress" view="ProgressBar"><Expressions/><location startLine="156" startOffset="16" endLine="165" endOffset="41"/></Target><Target id="@+id/progressText" view="TextView"><Expressions/><location startLine="167" startOffset="16" endLine="174" endOffset="38"/></Target><Target id="@+id/downloadSize" view="TextView"><Expressions/><location startLine="199" startOffset="20" endLine="205" endOffset="51"/></Target><Target id="@+id/downloadSpeed" view="TextView"><Expressions/><location startLine="223" startOffset="20" endLine="229" endOffset="47"/></Target><Target id="@+id/downloadEta" view="TextView"><Expressions/><location startLine="247" startOffset="20" endLine="253" endOffset="45"/></Target></Targets></Layout>