%com.musicplayer.pro.EqualizerActivity com.musicplayer.pro.MainActivity1com.musicplayer.pro.MainActivity.MainPagerAdapter*com.musicplayer.pro.MusicPlayerApplication$com.musicplayer.pro.SettingsActivity5com.musicplayer.pro.SettingsActivity.SettingsFragment4com.musicplayer.pro.adapters.AdvancedDownloadAdapterOcom.musicplayer.pro.adapters.AdvancedDownloadAdapter.AdvancedDownloadViewHolder,com.musicplayer.pro.adapters.DownloadAdapter?com.musicplayer.pro.adapters.DownloadAdapter.DownloadViewHolder-com.musicplayer.pro.adapters.EqualizerAdapter<com.musicplayer.pro.adapters.EqualizerAdapter.BandViewHolder(com.musicplayer.pro.adapters.SongAdapter7com.musicplayer.pro.adapters.SongAdapter.SongViewHolder0com.musicplayer.pro.database.SimpleMusicDatabase6com.musicplayer.pro.fragments.AdvancedDownloadFragment.com.musicplayer.pro.fragments.DownloadFragment*com.musicplayer.pro.fragments.MainFragment0com.musicplayer.pro.fragments.NowPlayingFragment&com.musicplayer.pro.managers.CacheType#com.musicplayer.pro.models.Download)com.musicplayer.pro.models.DownloadStatus#com.musicplayer.pro.models.Playlist'com.musicplayer.pro.models.PlaylistType%com.musicplayer.pro.models.RepeatModecom.musicplayer.pro.models.Song)com.musicplayer.pro.services.MusicService5com.musicplayer.pro.services.MusicService.MusicBinder8com.musicplayer.pro.viewmodels.AdvancedDownloadViewModel0com.musicplayer.pro.viewmodels.DownloadViewModel-com.musicplayer.pro.viewmodels.MusicViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 