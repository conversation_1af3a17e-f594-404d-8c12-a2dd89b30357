// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSimpleDownloadBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView artistText;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView statusText;

  @NonNull
  public final TextView titleText;

  private ItemSimpleDownloadBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView artistText, @NonNull ProgressBar progressBar, @NonNull TextView statusText,
      @NonNull TextView titleText) {
    this.rootView = rootView;
    this.artistText = artistText;
    this.progressBar = progressBar;
    this.statusText = statusText;
    this.titleText = titleText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSimpleDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSimpleDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_simple_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSimpleDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.artistText;
      TextView artistText = ViewBindings.findChildViewById(rootView, id);
      if (artistText == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      id = R.id.titleText;
      TextView titleText = ViewBindings.findChildViewById(rootView, id);
      if (titleText == null) {
        break missingId;
      }

      return new ItemSimpleDownloadBinding((MaterialCardView) rootView, artistText, progressBar,
          statusText, titleText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
