package com.musicplayer.pro.repositories

import android.content.Context
import android.provider.MediaStore
import com.musicplayer.pro.models.Song
import com.musicplayer.pro.models.Album
import com.musicplayer.pro.models.Artist
import com.musicplayer.pro.models.Playlist
import com.musicplayer.pro.viewmodels.SearchResults

/**
 * مستودع الموسيقى
 */
class MusicRepository(private val context: Context) {

    /**
     * الحصول على جميع الأغاني
     */
    suspend fun getAllSongs(): List<Song> {
        val songs = mutableListOf<Song>()

        try {
            val projection = arrayOf(
                MediaStore.Audio.Media._ID,
                MediaStore.Audio.Media.TITLE,
                MediaStore.Audio.Media.ARTIST,
                MediaStore.Audio.Media.ALBUM,
                MediaStore.Audio.Media.DURATION,
                MediaStore.Audio.Media.DATA,
                MediaStore.Audio.Media.ALBUM_ID,
                MediaStore.Audio.Media.ARTIST_ID,
                MediaStore.Audio.Media.YEAR,
                MediaStore.Audio.Media.SIZE
            )

            val selection = "${MediaStore.Audio.Media.IS_MUSIC} = 1"
            val sortOrder = "${MediaStore.Audio.Media.TITLE} ASC"

            val cursor = context.contentResolver.query(
                MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                projection,
                selection,
                null,
                sortOrder
            )

            cursor?.use {
                val idColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media._ID)
                val titleColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.TITLE)
                val artistColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ARTIST)
                val albumColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ALBUM)
                val durationColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION)
                val dataColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)
                val albumIdColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ALBUM_ID)
                val artistIdColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ARTIST_ID)
                val yearColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.YEAR)
                val sizeColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE)

                while (it.moveToNext()) {
                    val id = it.getLong(idColumn)
                    val title = it.getString(titleColumn) ?: "Unknown Title"
                    val artist = it.getString(artistColumn) ?: "Unknown Artist"
                    val album = it.getString(albumColumn) ?: "Unknown Album"
                    val duration = it.getLong(durationColumn)
                    val path = it.getString(dataColumn) ?: ""
                    val albumId = it.getLong(albumIdColumn)
                    val artistId = it.getLong(artistIdColumn)
                    val year = it.getInt(yearColumn)
                    val size = it.getLong(sizeColumn)

                    val song = Song(
                        id = id,
                        title = title,
                        artist = artist,
                        album = album,
                        duration = duration,
                        path = path,
                        albumId = albumId,
                        artistId = artistId,
                        year = year,
                        size = size
                    )

                    songs.add(song)
                }
            }

            android.util.Log.d("MusicRepository", "تم جلب ${songs.size} أغنية")

        } catch (e: Exception) {
            android.util.Log.e("MusicRepository", "خطأ في جلب الأغاني: ${e.message}")
        }

        return songs
    }

    /**
     * الحصول على جميع الألبومات
     */
    suspend fun getAllAlbums(): List<Album> {
        // سيتم تنفيذها لاحقاً
        return emptyList()
    }

    /**
     * الحصول على جميع الفنانين
     */
    suspend fun getAllArtists(): List<Artist> {
        // سيتم تنفيذها لاحقاً
        return emptyList()
    }

    /**
     * البحث في الأغاني
     */
    suspend fun search(query: String): SearchResults {
        val allSongs = getAllSongs()
        val filteredSongs = allSongs.filter {
            it.title.contains(query, ignoreCase = true) ||
            it.artist.contains(query, ignoreCase = true) ||
            it.album.contains(query, ignoreCase = true)
        }

        return SearchResults(songs = filteredSongs)
    }

    /**
     * الحصول على أغاني ألبوم
     */
    suspend fun getAlbumSongs(albumId: Long): List<Song> {
        return getAllSongs().filter { it.albumId == albumId }
    }

    /**
     * الحصول على أغاني فنان
     */
    suspend fun getArtistSongs(artistId: Long): List<Song> {
        return getAllSongs().filter { it.artistId == artistId }
    }

    /**
     * الحصول على جميع قوائم التشغيل
     */
    suspend fun getAllPlaylists(): List<Playlist> {
        // مؤقتاً نرجع قائمة فارغة
        return emptyList()
    }
    
    /**
     * تحديث أغنية
     */
    suspend fun updateSong(song: Song) {
        // منطق تحديث الأغنية
    }
    
    /**
     * إدراج قائمة تشغيل
     */
    suspend fun insertPlaylist(playlist: Playlist) {
        // منطق إدراج قائمة التشغيل
    }
    
    /**
     * حذف قائمة تشغيل
     */
    suspend fun deletePlaylist(playlist: Playlist) {
        // منطق حذف قائمة التشغيل
    }
}
