package com.musicplayer.pro.managers

import android.content.Context
import android.content.ContentValues
import android.provider.MediaStore
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.models.DownloadStatus
import kotlinx.coroutines.delay
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * مدير التحميلات - مطابق لـ DownloadManager في Python
 * يدير تحميل الملفات من YouTube وSoundCloud وغيرها
 */
class DownloadManager(private val context: Context) {
    
    // قاموس التحميلات النشطة
    private val activeDownloads = ConcurrentHashMap<String, Download>()

    // مجلد التحميلات
    private val downloadsDir = File(context.getExternalFilesDir(null), "Downloads")

    // مستمع اكتمال التحميل
    private var onDownloadCompletedListener: ((Download) -> Unit)? = null
    
    init {
        // إنشاء مجلد التحميلات إذا لم يكن موجوداً
        if (!downloadsDir.exists()) {
            downloadsDir.mkdirs()
        }
    }
    
    /**
     * الحصول على جميع التحميلات
     */
    suspend fun getAllDownloads(): List<Download> {
        android.util.Log.d("DownloadManager", "طلب جميع التحميلات، العدد: ${activeDownloads.size}")
        // في التطبيق الحقيقي، سيتم تحميل البيانات من قاعدة البيانات
        val downloads = activeDownloads.values.toList()
        downloads.forEach { download ->
            android.util.Log.d("DownloadManager", "تحميل: ${download.id} - ${download.title} - ${download.status}")
        }
        return downloads
    }
    
    /**
     * بدء تحميل جديد
     */
    suspend fun startDownload(download: Download) {
        try {
            android.util.Log.d("DownloadManager", "بدء التحميل: ${download.id} - ${download.title}")

            // إضافة التحميل إلى القائمة النشطة مع حالة DOWNLOADING مباشرة
            val downloadWithDetails = download.copy(
                status = DownloadStatus.DOWNLOADING,
                totalSize = 5 * 1024 * 1024L, // 5 MB
                progress = 0
            )
            activeDownloads[download.id] = downloadWithDetails
            android.util.Log.d("DownloadManager", "تم إضافة التحميل للقائمة النشطة بحالة DOWNLOADING")

            // بدء محاكاة عملية التحميل فوراً
            simulateDownloadSimple(downloadWithDetails)

        } catch (e: Exception) {
            android.util.Log.e("DownloadManager", "خطأ في بدء التحميل: ${e.message}")
            // تحديث حالة التحميل إلى فاشل
            activeDownloads[download.id] = download.copy(
                status = DownloadStatus.FAILED,
                errorMessage = e.message
            )
        }
    }

    /**
     * محاكاة تحميل مبسطة
     */
    private suspend fun simulateDownloadSimple(download: Download) {
        android.util.Log.d("DownloadManager", "بدء محاكاة التحميل المبسطة لـ: ${download.id}")

        for (progress in 0..100 step 10) {
            // فحص إذا تم إيقاف التحميل
            val currentDownload = activeDownloads[download.id]
            if (currentDownload?.status != DownloadStatus.DOWNLOADING) {
                android.util.Log.d("DownloadManager", "تم إيقاف التحميل: ${download.id}")
                break
            }

            val downloadedSize = (download.totalSize * progress) / 100
            val speed = "${(5..15).random()}.${(0..9).random()} MB/s"
            val eta = "${(100 - progress) * 2}s"

            // تحديث التقدم
            val updatedDownload = currentDownload.copy(
                progress = progress,
                downloadedSize = downloadedSize,
                speed = speed,
                eta = eta
            )
            activeDownloads[download.id] = updatedDownload

            android.util.Log.d("DownloadManager", "تحديث التقدم: ${download.id} - $progress%")

            // انتظار
            delay(1000)
        }

        // إكمال التحميل
        val finalDownload = activeDownloads[download.id]
        if (finalDownload?.status == DownloadStatus.DOWNLOADING) {
            val filePath = File(downloadsDir, "${download.title}.${download.format}").absolutePath

            // إنشاء ملف وهمي للاختبار
            try {
                val file = File(filePath)
                file.parentFile?.mkdirs()
                file.createNewFile()
                android.util.Log.d("DownloadManager", "تم إنشاء الملف: $filePath")
            } catch (e: Exception) {
                android.util.Log.e("DownloadManager", "خطأ في إنشاء الملف: ${e.message}")
            }

            val completedDownload = finalDownload.copy(
                status = DownloadStatus.COMPLETED,
                progress = 100,
                downloadedSize = download.totalSize,
                filePath = filePath,
                dateCompleted = System.currentTimeMillis(),
                speed = "",
                eta = ""
            )

            activeDownloads[download.id] = completedDownload

            // إضافة الأغنية إلى مكتبة الأغاني
            addToMusicLibrary(completedDownload)

            // إشعار المستمعين باكتمال التحميل
            onDownloadCompletedListener?.invoke(completedDownload)

            android.util.Log.d("DownloadManager", "تم إكمال التحميل: ${download.id}")
        }
    }
    
    /**
     * إيقاف تحميل مؤقتاً
     */
    suspend fun pauseDownload(downloadId: String) {
        activeDownloads[downloadId]?.let { download ->
            activeDownloads[downloadId] = download.copy(status = DownloadStatus.PAUSED)
        }
    }
    
    /**
     * استئناف تحميل
     */
    suspend fun resumeDownload(downloadId: String) {
        activeDownloads[downloadId]?.let { download ->
            activeDownloads[downloadId] = download.copy(status = DownloadStatus.DOWNLOADING)
            // استئناف عملية التحميل
            simulateDownload(download)
        }
    }
    
    /**
     * إلغاء تحميل
     */
    suspend fun cancelDownload(downloadId: String) {
        activeDownloads[downloadId]?.let { download ->
            activeDownloads[downloadId] = download.copy(status = DownloadStatus.CANCELLED)
            
            // حذف الملف الجزئي إذا كان موجوداً
            val file = File(downloadsDir, "${download.title}.${download.format}")
            if (file.exists()) {
                file.delete()
            }
        }
    }
    
    /**
     * إعادة محاولة تحميل
     */
    suspend fun retryDownload(downloadId: String) {
        activeDownloads[downloadId]?.let { download ->
            startDownload(download.copy(
                status = DownloadStatus.PENDING,
                progress = 0,
                downloadedSize = 0,
                errorMessage = null
            ))
        }
    }
    
    /**
     * حذف تحميل
     */
    suspend fun deleteDownload(downloadId: String) {
        activeDownloads[downloadId]?.let { download ->
            // حذف الملف من التخزين
            val file = File(downloadsDir, "${download.title}.${download.format}")
            if (file.exists()) {
                file.delete()
            }
            
            // إزالة من القائمة النشطة
            activeDownloads.remove(downloadId)
        }
    }
    
    /**
     * تشغيل ملف محمل
     */
    suspend fun playDownloadedFile(download: Download) {
        if (download.status == DownloadStatus.COMPLETED) {
            val file = File(download.filePath)
            if (file.exists()) {
                // هنا يمكن إرسال الملف إلى مشغل الموسيقى
                // أو إضافته إلى قائمة التشغيل
            }
        }
    }
    
    /**
     * محاكاة عملية التحميل
     * في التطبيق الحقيقي، سيتم استخدام yt-dlp أو مكتبة مشابهة
     */
    private suspend fun simulateDownload(download: Download) {
        android.util.Log.d("DownloadManager", "بدء محاكاة التحميل لـ: ${download.id}")

        val totalSize = 5 * 1024 * 1024L // 5 MB كمثال
        var downloadedSize = 0L

        // تحديث الحجم الإجمالي
        activeDownloads[download.id] = download.copy(totalSize = totalSize)
        android.util.Log.d("DownloadManager", "تم تحديث الحجم الإجمالي: $totalSize")
        
        // محاكاة التحميل بخطوات أصغر لتحديث أكثر سلاسة
        for (progress in 0..100 step 2) {
            // فحص إذا تم إيقاف التحميل
            val currentDownload = activeDownloads[download.id]
            if (currentDownload?.status != DownloadStatus.DOWNLOADING) {
                break
            }

            downloadedSize = (totalSize * progress) / 100
            val speed = "${(5..15).random()}.${(0..9).random()} MB/s" // سرعة وهمية متغيرة
            val eta = "${(100 - progress) * 3}s" // وقت متبقي وهمي

            // تحديث التقدم
            activeDownloads[download.id] = currentDownload.copy(
                progress = progress,
                downloadedSize = downloadedSize,
                speed = speed,
                eta = eta
            )

            // انتظار أقصر لتحديث أسرع
            delay(200)
        }
        
        // إكمال التحميل
        val finalDownload = activeDownloads[download.id]
        if (finalDownload?.status == DownloadStatus.DOWNLOADING) {
            val filePath = File(downloadsDir, "${download.title}.${download.format}").absolutePath
            
            activeDownloads[download.id] = finalDownload.copy(
                status = DownloadStatus.COMPLETED,
                progress = 100,
                downloadedSize = totalSize,
                filePath = filePath,
                dateCompleted = System.currentTimeMillis(),
                speed = "",
                eta = ""
            )
            
            // إنشاء ملف وهمي
            createDummyFile(filePath)
        }
    }
    
    /**
     * إنشاء ملف وهمي للاختبار
     */
    private fun createDummyFile(filePath: String) {
        try {
            val file = File(filePath)
            file.createNewFile()
            file.writeText("This is a dummy downloaded file for testing purposes.")
        } catch (e: Exception) {
            // تجاهل الأخطاء في إنشاء الملف الوهمي
        }
    }
    
    /**
     * استخراج معلومات الفيديو من الرابط
     * في التطبيق الحقيقي، سيتم استخدام yt-dlp
     */
    private suspend fun extractVideoInfo(url: String): VideoInfo {
        // محاكاة استخراج المعلومات
        delay(1000)
        
        return when {
            url.contains("youtube.com") || url.contains("youtu.be") -> {
                VideoInfo(
                    title = "YouTube Video Title",
                    artist = "YouTube Channel",
                    thumbnail = "https://example.com/thumbnail.jpg",
                    duration = 180000 // 3 minutes
                )
            }
            url.contains("soundcloud.com") -> {
                VideoInfo(
                    title = "SoundCloud Track",
                    artist = "SoundCloud Artist",
                    thumbnail = "https://example.com/thumbnail.jpg",
                    duration = 240000 // 4 minutes
                )
            }
            else -> {
                VideoInfo(
                    title = "Unknown Media",
                    artist = "Unknown Artist",
                    thumbnail = null,
                    duration = 0
                )
            }
        }
    }
    
    /**
     * تعيين مستمع اكتمال التحميل
     */
    fun setOnDownloadCompletedListener(listener: (Download) -> Unit) {
        onDownloadCompletedListener = listener
    }

    /**
     * إضافة الأغنية المحملة إلى مكتبة الأغاني
     */
    private fun addToMusicLibrary(download: Download) {
        try {
            android.util.Log.d("DownloadManager", "إضافة الأغنية لمكتبة الأغاني: ${download.title}")

            // إضافة الملف إلى MediaStore لجعله مرئياً في مكتبة الأغاني
            val contentValues = ContentValues().apply {
                put(MediaStore.Audio.Media.DISPLAY_NAME, "${download.title}.${download.format}")
                put(MediaStore.Audio.Media.TITLE, download.title)
                put(MediaStore.Audio.Media.ARTIST, download.artist)
                put(MediaStore.Audio.Media.ALBUM, "Downloaded Music")
                put(MediaStore.Audio.Media.DURATION, download.duration * 1000L) // تحويل إلى milliseconds
                put(MediaStore.Audio.Media.MIME_TYPE, "audio/${download.format}")
                put(MediaStore.Audio.Media.SIZE, download.totalSize)
                put(MediaStore.Audio.Media.DATA, download.filePath)
                put(MediaStore.Audio.Media.IS_DOWNLOAD, 1)
                put(MediaStore.Audio.Media.DATE_ADDED, System.currentTimeMillis() / 1000)
                put(MediaStore.Audio.Media.DATE_MODIFIED, System.currentTimeMillis() / 1000)
            }

            // إدراج في MediaStore
            val uri = context.contentResolver.insert(
                MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                contentValues
            )

            if (uri != null) {
                android.util.Log.d("DownloadManager", "تم إضافة الأغنية لـ MediaStore: $uri")

                // إشعار MediaScanner لتحديث مكتبة الأغاني
                android.media.MediaScannerConnection.scanFile(
                    context,
                    arrayOf(download.filePath),
                    arrayOf("audio/${download.format}")
                ) { path, uri ->
                    android.util.Log.d("DownloadManager", "تم مسح الملف: $path -> $uri")
                }
            } else {
                android.util.Log.e("DownloadManager", "فشل في إضافة الأغنية لـ MediaStore")
            }

        } catch (e: Exception) {
            android.util.Log.e("DownloadManager", "خطأ في إضافة الأغنية لمكتبة الأغاني: ${e.message}")
        }
    }

    /**
     * معلومات الفيديو
     */
    data class VideoInfo(
        val title: String,
        val artist: String,
        val thumbnail: String?,
        val duration: Long
    )
}
