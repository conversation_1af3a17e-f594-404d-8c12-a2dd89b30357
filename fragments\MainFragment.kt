package com.musicplayer.pro.fragments

import android.os.Bundle
import android.view.*
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.appcompat.widget.SearchView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.tabs.TabLayout
import com.musicplayer.pro.R
import com.musicplayer.pro.adapters.SongAdapter
import com.musicplayer.pro.adapters.AlbumAdapter
import com.musicplayer.pro.adapters.ArtistAdapter
import com.musicplayer.pro.adapters.PlaylistAdapter
import com.musicplayer.pro.models.Song
import com.musicplayer.pro.models.Album
import com.musicplayer.pro.models.Artist
import com.musicplayer.pro.models.Playlist
import com.musicplayer.pro.viewmodels.MainViewModel
import com.musicplayer.pro.utils.ThemeManager
import com.musicplayer.pro.utils.AudioScanner

/**
 * الشاشة الرئيسية للتطبيق
 * تعرض قوائم الأغاني والألبومات والفنانين وقوائم التشغيل
 */
class MainFragment : Fragment() {
    
    companion object {
        private const val TAG = "MainFragment"
    }
    
    // ViewModel
    private lateinit var viewModel: MainViewModel
    
    // UI Components
    private lateinit var tabLayout: TabLayout
    private lateinit var recyclerView: RecyclerView
    private lateinit var searchView: SearchView
    private lateinit var fabShuffle: FloatingActionButton
    
    // Adapters
    private lateinit var songAdapter: SongAdapter
    private lateinit var albumAdapter: AlbumAdapter
    private lateinit var artistAdapter: ArtistAdapter
    private lateinit var playlistAdapter: PlaylistAdapter
    
    // Current tab
    private var currentTab = 0
    
    // Search
    private var isSearchActive = false
    private var searchQuery = ""
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_main, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // تهيئة ViewModel
        viewModel = ViewModelProvider(this)[MainViewModel::class.java]
        
        // تهيئة UI
        initializeViews(view)
        setupRecyclerView()
        setupTabLayout()
        setupSearchView()
        setupFab()
        
        // مراقبة البيانات
        observeData()
        
        // تحميل البيانات
        loadData()
    }
    
    /**
     * تهيئة العناصر
     */
    private fun initializeViews(view: View) {
        tabLayout = view.findViewById(R.id.tabLayout)
        recyclerView = view.findViewById(R.id.recyclerView)
        searchView = view.findViewById(R.id.searchView)
        fabShuffle = view.findViewById(R.id.fabShuffle)
    }
    
    /**
     * إعداد RecyclerView
     */
    private fun setupRecyclerView() {
        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        
        // إنشاء المحولات
        songAdapter = SongAdapter { song, position ->
            onSongClicked(song, position)
        }
        
        albumAdapter = AlbumAdapter { album ->
            onAlbumClicked(album)
        }
        
        artistAdapter = ArtistAdapter { artist ->
            onArtistClicked(artist)
        }
        
        playlistAdapter = PlaylistAdapter { playlist ->
            onPlaylistClicked(playlist)
        }
        
        // تعيين المحول الافتراضي
        recyclerView.adapter = songAdapter
    }
    
    /**
     * إعداد TabLayout
     */
    private fun setupTabLayout() {
        // إضافة التابات
        tabLayout.addTab(tabLayout.newTab().setText(getString(R.string.songs)).setIcon(R.drawable.ic_music_note))
        tabLayout.addTab(tabLayout.newTab().setText(getString(R.string.albums)).setIcon(R.drawable.ic_album))
        tabLayout.addTab(tabLayout.newTab().setText(getString(R.string.artists)).setIcon(R.drawable.ic_artist))
        tabLayout.addTab(tabLayout.newTab().setText(getString(R.string.playlists)).setIcon(R.drawable.ic_playlist))
        
        // مستمع تغيير التاب
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let {
                    currentTab = it.position
                    switchTab(currentTab)
                }
            }
            
            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }
    
    /**
     * إعداد شريط البحث
     */
    private fun setupSearchView() {
        searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                query?.let { performSearch(it) }
                return true
            }
            
            override fun onQueryTextChange(newText: String?): Boolean {
                searchQuery = newText ?: ""
                if (searchQuery.isEmpty()) {
                    clearSearch()
                } else {
                    performSearch(searchQuery)
                }
                return true
            }
        })
        
        searchView.setOnSearchClickListener {
            isSearchActive = true
            fabShuffle.hide()
        }
        
        searchView.setOnCloseListener {
            isSearchActive = false
            clearSearch()
            fabShuffle.show()
            false
        }
    }
    
    /**
     * إعداد زر التشغيل العشوائي
     */
    private fun setupFab() {
        fabShuffle.setOnClickListener {
            when (currentTab) {
                0 -> viewModel.shuffleAllSongs()
                1 -> viewModel.shuffleAllAlbums()
                2 -> viewModel.shuffleAllArtists()
                3 -> viewModel.shuffleAllPlaylists()
            }
        }
    }
    
    /**
     * تبديل التاب
     */
    private fun switchTab(tabIndex: Int) {
        when (tabIndex) {
            0 -> {
                recyclerView.adapter = songAdapter
                fabShuffle.setImageResource(R.drawable.ic_shuffle)
                if (isSearchActive) {
                    performSearch(searchQuery)
                } else {
                    viewModel.loadSongs()
                }
            }
            1 -> {
                recyclerView.adapter = albumAdapter
                fabShuffle.setImageResource(R.drawable.ic_shuffle)
                if (isSearchActive) {
                    performSearch(searchQuery)
                } else {
                    viewModel.loadAlbums()
                }
            }
            2 -> {
                recyclerView.adapter = artistAdapter
                fabShuffle.setImageResource(R.drawable.ic_shuffle)
                if (isSearchActive) {
                    performSearch(searchQuery)
                } else {
                    viewModel.loadArtists()
                }
            }
            3 -> {
                recyclerView.adapter = playlistAdapter
                fabShuffle.setImageResource(R.drawable.ic_playlist_play)
                if (isSearchActive) {
                    performSearch(searchQuery)
                } else {
                    viewModel.loadPlaylists()
                }
            }
        }
    }
    
    /**
     * مراقبة البيانات
     */
    private fun observeData() {
        // مراقبة الأغاني
        viewModel.songs.observe(viewLifecycleOwner) { songs ->
            if (currentTab == 0) {
                songAdapter.updateSongs(songs)
            }
        }
        
        // مراقبة الألبومات
        viewModel.albums.observe(viewLifecycleOwner) { albums ->
            if (currentTab == 1) {
                albumAdapter.updateAlbums(albums)
            }
        }
        
        // مراقبة الفنانين
        viewModel.artists.observe(viewLifecycleOwner) { artists ->
            if (currentTab == 2) {
                artistAdapter.updateArtists(artists)
            }
        }
        
        // مراقبة قوائم التشغيل
        viewModel.playlists.observe(viewLifecycleOwner) { playlists ->
            if (currentTab == 3) {
                playlistAdapter.updatePlaylists(playlists)
            }
        }
        
        // مراقبة نتائج البحث
        viewModel.searchResults.observe(viewLifecycleOwner) { results ->
            if (isSearchActive) {
                when (currentTab) {
                    0 -> songAdapter.updateSongs(results.songs)
                    1 -> albumAdapter.updateAlbums(results.albums)
                    2 -> artistAdapter.updateArtists(results.artists)
                    3 -> playlistAdapter.updatePlaylists(results.playlists)
                }
            }
        }
        
        // مراقبة حالة التحميل
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // عرض/إخفاء مؤشر التحميل
            updateLoadingState(isLoading)
        }
    }
    
    /**
     * تحميل البيانات
     */
    private fun loadData() {
        viewModel.loadSongs()
    }
    
    /**
     * تنفيذ البحث
     */
    private fun performSearch(query: String) {
        viewModel.search(query)
    }
    
    /**
     * مسح البحث
     */
    private fun clearSearch() {
        searchQuery = ""
        isSearchActive = false
        switchTab(currentTab)
    }
    
    /**
     * تحديث حالة التحميل
     */
    private fun updateLoadingState(isLoading: Boolean) {
        // يمكن إضافة مؤشر تحميل هنا
    }
    
    /**
     * عند النقر على أغنية
     */
    private fun onSongClicked(song: Song, position: Int) {
        val currentSongs = if (isSearchActive) {
            viewModel.searchResults.value?.songs ?: emptyList()
        } else {
            viewModel.songs.value ?: emptyList()
        }
        
        // تشغيل الأغنية
        viewModel.playSong(song, currentSongs)
    }
    
    /**
     * عند النقر على ألبوم
     */
    private fun onAlbumClicked(album: Album) {
        // الانتقال لشاشة الألبوم
        viewModel.openAlbum(album)
    }
    
    /**
     * عند النقر على فنان
     */
    private fun onArtistClicked(artist: Artist) {
        // الانتقال لشاشة الفنان
        viewModel.openArtist(artist)
    }
    
    /**
     * عند النقر على قائمة تشغيل
     */
    private fun onPlaylistClicked(playlist: Playlist) {
        // الانتقال لشاشة قائمة التشغيل
        viewModel.openPlaylist(playlist)
    }
    
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.main_menu, menu)
        super.onCreateOptionsMenu(menu, inflater)
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_scan -> {
                viewModel.scanForMusic()
                true
            }
            R.id.action_settings -> {
                // فتح الإعدادات
                true
            }
            R.id.action_sort -> {
                // فتح خيارات الترتيب
                showSortOptions()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    /**
     * عرض خيارات الترتيب
     */
    private fun showSortOptions() {
        // عرض حوار خيارات الترتيب
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onResume() {
        super.onResume()
        // تحديث مكتبة الأغاني عند العودة للشاشة
        // هذا يضمن ظهور الأغاني المحملة حديثاً
        viewModel.refreshMusicLibrary()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // إلغاء تسجيل المستمعين
        viewModel.unregisterMusicLibraryUpdateReceiver()
    }
}
