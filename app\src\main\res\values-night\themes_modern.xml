<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- Dark Theme Base -->
    <style name="Theme.MusicPlayer.Modern" parent="Theme.Material3.DayNight">
        <!-- Primary Colors -->
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_onPrimaryContainer</item>
        
        <!-- Secondary Colors -->
        <item name="colorSecondary">@color/md_theme_dark_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_dark_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_dark_onSecondaryContainer</item>
        
        <!-- Tertiary Colors -->
        <item name="colorTertiary">@color/md_theme_dark_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_dark_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_dark_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_dark_onTertiaryContainer</item>
        
        <!-- Error Colors -->
        <item name="colorError">@color/md_theme_dark_error</item>
        <item name="colorOnError">@color/md_theme_dark_onError</item>
        <item name="colorErrorContainer">@color/md_theme_dark_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_dark_onErrorContainer</item>
        
        <!-- Background Colors -->
        <item name="android:colorBackground">@color/md_theme_dark_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_onBackground</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_onSurface</item>
        
        <!-- Surface Variants -->
        <item name="colorSurfaceVariant">@color/md_theme_dark_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_dark_outline</item>
        
        <!-- Status Bar -->
        <item name="android:statusBarColor">@color/md_theme_dark_surface</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation Bar -->
        <item name="android:navigationBarColor">@color/md_theme_dark_surface</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        <!-- Window -->
        <item name="android:windowBackground">@color/md_theme_dark_background</item>
    </style>
    
    <!-- Dark Purple Theme -->
    <style name="Theme.MusicPlayer.Purple" parent="Theme.MusicPlayer.Modern">
        <item name="colorPrimary">#C084FC</item>
        <item name="colorPrimaryContainer">#581C87</item>
        <item name="colorSecondary">#A855F7</item>
        <item name="colorSecondaryContainer">#4C1D95</item>
        <item name="colorTertiary">#DDD6FE</item>
        <item name="colorTertiaryContainer">#6B21A8</item>
    </style>
    
    <!-- Dark Blue Theme -->
    <style name="Theme.MusicPlayer.Blue" parent="Theme.MusicPlayer.Modern">
        <item name="colorPrimary">#60A5FA</item>
        <item name="colorPrimaryContainer">#1E3A8A</item>
        <item name="colorSecondary">#3B82F6</item>
        <item name="colorSecondaryContainer">#1E40AF</item>
        <item name="colorTertiary">#BFDBFE</item>
        <item name="colorTertiaryContainer">#1D4ED8</item>
    </style>
    
    <!-- Dark Green Theme -->
    <style name="Theme.MusicPlayer.Green" parent="Theme.MusicPlayer.Modern">
        <item name="colorPrimary">#34D399</item>
        <item name="colorPrimaryContainer">#064E3B</item>
        <item name="colorSecondary">#10B981</item>
        <item name="colorSecondaryContainer">#065F46</item>
        <item name="colorTertiary">#A7F3D0</item>
        <item name="colorTertiaryContainer">#047857</item>
    </style>
    
    <!-- Dark Pink Theme -->
    <style name="Theme.MusicPlayer.Pink" parent="Theme.MusicPlayer.Modern">
        <item name="colorPrimary">#F472B6</item>
        <item name="colorPrimaryContainer">#831843</item>
        <item name="colorSecondary">#EC4899</item>
        <item name="colorSecondaryContainer">#9D174D</item>
        <item name="colorTertiary">#FBCFE8</item>
        <item name="colorTertiaryContainer">#BE185D</item>
    </style>
    
    <!-- Dark Orange Theme -->
    <style name="Theme.MusicPlayer.Orange" parent="Theme.MusicPlayer.Modern">
        <item name="colorPrimary">#FBBF24</item>
        <item name="colorPrimaryContainer">#92400E</item>
        <item name="colorSecondary">#F59E0B</item>
        <item name="colorSecondaryContainer">#B45309</item>
        <item name="colorTertiary">#FDE68A</item>
        <item name="colorTertiaryContainer">#D97706</item>
    </style>
    
    <!-- AMOLED Black Theme -->
    <style name="Theme.MusicPlayer.AMOLED" parent="Theme.MusicPlayer.Modern">
        <item name="android:colorBackground">#000000</item>
        <item name="colorSurface">#000000</item>
        <item name="colorSurfaceVariant">#1A1A1A</item>
        <item name="android:windowBackground">#000000</item>
        <item name="android:statusBarColor">#000000</item>
        <item name="android:navigationBarColor">#000000</item>
        <item name="colorOnSurface">#FFFFFF</item>
        <item name="colorOnBackground">#FFFFFF</item>
    </style>
    
</resources>
