package com.musicplayer.pro.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import com.musicplayer.pro.R
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.models.DownloadStatus

/**
 * محول قائمة التحميلات
 */
class DownloadAdapter(
    private val onDownloadClick: (Download) -> Unit,
    private val onPauseClick: (Download) -> Unit,
    private val onResumeClick: (Download) -> Unit,
    private val onCancelClick: (Download) -> Unit,
    private val onRetryClick: (Download) -> Unit,
    private val onDeleteClick: (Download) -> Unit
) : RecyclerView.Adapter<DownloadAdapter.DownloadViewHolder>() {
    
    private var downloads: List<Download> = emptyList()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DownloadViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_download, parent, false)
        return DownloadViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: DownloadViewHolder, position: Int) {
        val download = downloads[position]
        holder.bind(download)
    }
    
    override fun getItemCount(): Int = downloads.size
    
    /**
     * تحديث قائمة التحميلات
     */
    fun updateDownloads(newDownloads: List<Download>) {
        downloads = newDownloads
        notifyDataSetChanged()
    }

    /**
     * تحديث تقدم التحميلات
     */
    fun updateProgress(progressMap: Map<String, Int>) {
        // تحديث التقدم للتحميلات المرئية فقط
        for (i in 0 until itemCount) {
            val download = downloads.getOrNull(i)
            if (download != null && progressMap.containsKey(download.id)) {
                notifyItemChanged(i)
            }
        }
    }
    
    inner class DownloadViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleTextView: TextView = itemView.findViewById(R.id.downloadTitle)
        private val artistTextView: TextView = itemView.findViewById(R.id.downloadArtist)
        private val statusTextView: TextView = itemView.findViewById(R.id.downloadStatus)
        private val qualityTextView: TextView = itemView.findViewById(R.id.downloadQuality)
        private val progressBar: ProgressBar = itemView.findViewById(R.id.downloadProgress)
        private val progressTextView: TextView = itemView.findViewById(R.id.progressText)
        private val sizeTextView: TextView = itemView.findViewById(R.id.downloadSize)
        private val speedTextView: TextView = itemView.findViewById(R.id.downloadSpeed)
        private val etaTextView: TextView = itemView.findViewById(R.id.downloadEta)
        private val thumbnailImageView: ImageView = itemView.findViewById(R.id.downloadThumbnail)
        private val actionButton: ImageButton = itemView.findViewById(R.id.actionButton)
        private val deleteButton: ImageButton = itemView.findViewById(R.id.deleteButton)
        
        fun bind(download: Download) {
            // عرض العنوان مع تحسين النص
            titleTextView.text = if (download.title.isNotEmpty()) {
                download.title
            } else {
                "جاري تحميل الملف..."
            }

            // عرض الفنان مع تحسين النص
            artistTextView.text = if (download.artist.isNotEmpty()) {
                download.artist
            } else {
                "فنان غير معروف"
            }

            // عرض الحالة مع ألوان مختلفة
            statusTextView.text = getStatusText(download.status)
            statusTextView.setTextColor(getStatusColor(download.status))

            // عرض معلومات الجودة والتنسيق مع المدة
            val durationText = if (download.duration > 0) {
                " • ${formatDuration(download.duration)}"
            } else {
                ""
            }
            qualityTextView.text = "${download.format.uppercase()} • ${download.quality}$durationText"

            // تحديث شريط التقدم
            progressBar.progress = download.getProgressPercentage()
            progressTextView.text = "${download.getProgressPercentage()}%"

            // تحديث معلومات الحجم والسرعة
            if (download.totalSize > 0) {
                sizeTextView.text = "${download.getFormattedDownloadedSize()} / ${download.getFormattedTotalSize()}"
            } else {
                sizeTextView.text = "حجم غير معروف"
            }

            speedTextView.text = if (download.speed.isNotEmpty()) {
                download.speed
            } else {
                "-"
            }

            etaTextView.text = if (download.eta.isNotEmpty()) {
                download.eta
            } else {
                "-"
            }

            // تحديث الصورة المصغرة مع تحسين العرض
            updateThumbnail(download)

            // تحديث زر العمل حسب الحالة
            updateActionButton(download)

            // إعداد المستمعين
            itemView.setOnClickListener { onDownloadClick(download) }
            deleteButton.setOnClickListener { onDeleteClick(download) }
        }

        /**
         * تحديث الصورة المصغرة
         */
        private fun updateThumbnail(download: Download) {
            when {
                download.thumbnailUrl.contains("youtube.com") || download.thumbnailUrl.contains("youtu.be") -> {
                    thumbnailImageView.setImageResource(R.drawable.ic_music_library)
                }
                download.thumbnailUrl.contains("soundcloud") -> {
                    thumbnailImageView.setImageResource(R.drawable.ic_download)
                }
                download.thumbnailUrl.contains("spotify") -> {
                    thumbnailImageView.setImageResource(R.drawable.ic_now_playing)
                }
                download.thumbnailUrl.contains("bandcamp") -> {
                    thumbnailImageView.setImageResource(R.drawable.ic_music_library)
                }
                download.thumbnailUrl.contains("mixcloud") -> {
                    thumbnailImageView.setImageResource(R.drawable.ic_music_library)
                }
                download.thumbnailUrl.isNotEmpty() -> {
                    // في التطبيق الحقيقي، يمكن استخدام Glide لتحميل الصورة
                    thumbnailImageView.setImageResource(R.drawable.default_album_cover)
                }
                else -> {
                    thumbnailImageView.setImageResource(R.drawable.default_album_cover)
                }
            }
        }

        /**
         * الحصول على لون الحالة
         */
        private fun getStatusColor(status: DownloadStatus): Int {
            return when (status) {
                DownloadStatus.PENDING -> android.graphics.Color.GRAY
                DownloadStatus.DOWNLOADING -> android.graphics.Color.BLUE
                DownloadStatus.PAUSED -> android.graphics.Color.parseColor("#FF9800")
                DownloadStatus.COMPLETED -> android.graphics.Color.GREEN
                DownloadStatus.FAILED -> android.graphics.Color.RED
                DownloadStatus.CANCELLED -> android.graphics.Color.GRAY
            }
        }

        /**
         * تنسيق المدة
         */
        private fun formatDuration(seconds: Int): String {
            val minutes = seconds / 60
            val remainingSeconds = seconds % 60
            return if (minutes > 0) {
                "${minutes}:${String.format("%02d", remainingSeconds)}"
            } else {
                "${remainingSeconds}s"
            }
        }
        
        private fun updateActionButton(download: Download) {
            when (download.status) {
                DownloadStatus.PENDING -> {
                    actionButton.setImageResource(R.drawable.ic_pause)
                    actionButton.setOnClickListener { onPauseClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
                DownloadStatus.DOWNLOADING -> {
                    actionButton.setImageResource(R.drawable.ic_pause)
                    actionButton.setOnClickListener { onPauseClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
                DownloadStatus.PAUSED -> {
                    actionButton.setImageResource(R.drawable.ic_play)
                    actionButton.setOnClickListener { onResumeClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
                DownloadStatus.COMPLETED -> {
                    actionButton.setImageResource(R.drawable.ic_play)
                    actionButton.setOnClickListener { onDownloadClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
                DownloadStatus.FAILED -> {
                    actionButton.setImageResource(R.drawable.ic_refresh)
                    actionButton.setOnClickListener { onRetryClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
                DownloadStatus.CANCELLED -> {
                    actionButton.setImageResource(R.drawable.ic_refresh)
                    actionButton.setOnClickListener { onRetryClick(download) }
                    actionButton.visibility = View.VISIBLE
                }
            }
        }
        
        private fun getStatusText(status: DownloadStatus): String {
            return when (status) {
                DownloadStatus.PENDING -> "في الانتظار"
                DownloadStatus.DOWNLOADING -> "جاري التحميل"
                DownloadStatus.PAUSED -> "متوقف مؤقتاً"
                DownloadStatus.COMPLETED -> "مكتمل"
                DownloadStatus.FAILED -> "فاشل"
                DownloadStatus.CANCELLED -> "ملغي"
            }
        }
    }
}
