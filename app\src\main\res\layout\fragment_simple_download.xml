<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/background_primary">

    <!-- عنوان الصفحة -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="تحميل الأغاني"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- قسم إدخال الرابط -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="أدخل رابط الأغنية"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <!-- حقل الرابط -->
            <EditText
                android:id="@+id/urlEditText"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/edit_text_background"
                android:hint="https://youtube.com/watch?v=..."
                android:inputType="textUri"
                android:padding="12dp"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_secondary" />

            <!-- زر التحميل -->
            <Button
                android:id="@+id/downloadButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="بدء التحميل"
                android:textColor="@color/text_on_accent"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/button_primary_background" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- شريط التقدم -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:layout_marginBottom="8dp"
        android:visibility="gone"
        android:progressTint="@color/accent_primary" />

    <!-- نص الحالة -->
    <TextView
        android:id="@+id/statusText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="جاهز للتحميل"
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        android:gravity="center" />

    <!-- قائمة التحميلات -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="التحميلات"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/downloadsRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:paddingBottom="16dp" />

</LinearLayout>
