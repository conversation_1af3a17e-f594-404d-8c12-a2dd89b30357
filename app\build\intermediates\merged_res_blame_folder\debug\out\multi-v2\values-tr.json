{"logs": [{"outputFile": "com.musicplayer.pro.app-mergeDebugResources-59:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e081d70774c49b9b7dc6950692deb52\\transformed\\material-1.11.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1036,1127,1204,1265,1356,1419,1482,1541,1610,1673,1727,1835,1893,1955,2009,2082,2203,2287,2378,2518,2595,2671,2802,2889,2965,3018,3072,3138,3208,3285,3368,3448,3519,3594,3672,3743,3844,3929,4018,4113,4206,4278,4350,4446,4498,4584,4651,4735,4825,4887,4951,5014,5084,5178,5280,5369,5469,5526,5584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "260,335,410,487,586,677,773,885,967,1031,1122,1199,1260,1351,1414,1477,1536,1605,1668,1722,1830,1888,1950,2004,2077,2198,2282,2373,2513,2590,2666,2797,2884,2960,3013,3067,3133,3203,3280,3363,3443,3514,3589,3667,3738,3839,3924,4013,4108,4201,4273,4345,4441,4493,4579,4646,4730,4820,4882,4946,5009,5079,5173,5275,5364,5464,5521,5579,5658"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,66,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "747,3604,3679,3754,3831,3930,4734,4830,4942,5095,8999,9090,9167,9228,9319,9382,9445,9504,9573,9636,9690,9798,9856,9918,9972,10045,10166,10250,10341,10481,10558,10634,10765,10852,10928,10981,11035,11101,11171,11248,11331,11411,11482,11557,11635,11706,11807,11892,11981,12076,12169,12241,12313,12409,12461,12547,12614,12698,12788,12850,12914,12977,13047,13141,13243,13332,13432,13489,13847", "endLines": "22,50,51,52,53,54,62,63,64,66,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,180", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "907,3674,3749,3826,3925,4016,4825,4937,5019,5154,9085,9162,9223,9314,9377,9440,9499,9568,9631,9685,9793,9851,9913,9967,10040,10161,10245,10336,10476,10553,10629,10760,10847,10923,10976,11030,11096,11166,11243,11326,11406,11477,11552,11630,11701,11802,11887,11976,12071,12164,12236,12308,12404,12456,12542,12609,12693,12783,12845,12909,12972,13042,13136,13238,13327,13427,13484,13542,13921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f0c4d03d60aaed2968a93a146657af49\\transformed\\jetified-media3-exoplayer-1.2.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,191,256,317,397,469,559,655", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "127,186,251,312,392,464,554,650,726"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7126,7203,7262,7327,7388,7468,7540,7630,7726", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "7198,7257,7322,7383,7463,7535,7625,7721,7797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d0962e3cd95d785309666b77b5473090\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,1026,1125,1237,1322,1428,1548,1628,1703,1794,1887,1979,2073,2173,2266,2368,2463,2554,2645,2724,2831,2935,3031,3138,3241,3350,3506,14058", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "1021,1120,1232,1317,1423,1543,1623,1698,1789,1882,1974,2068,2168,2261,2363,2458,2549,2640,2719,2826,2930,3026,3133,3236,3345,3501,3599,14133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\210f3162f1672bd532c02d8af1bd50c1\\transformed\\preference-1.2.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,341,473,642,725", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "171,258,336,468,637,720,798"}, "to": {"startLines": "65,118,179,181,184,185,186", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5024,8912,13769,13926,14239,14408,14491", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "5090,8994,13842,14053,14403,14486,14564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7c108dd56627f30fe94755d1a2faeaf2\\transformed\\core-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "55,56,57,58,59,60,61,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4021,4118,4220,4318,4415,4517,4623,14138", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "4113,4215,4313,4410,4512,4618,4729,14234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e913642d7c50f47db3e63580f9572497\\transformed\\navigation-ui-2.7.6\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,116", "endOffsets": "155,272"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "13547,13652", "endColumns": "104,116", "endOffsets": "13647,13764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\472040ed58573b5e54239e60b254baef\\transformed\\jetified-media3-ui-1.2.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,652,741,834,909,994,1080,1155,1221,1306,1392,1460,1522,1582,1651,1768,1880,1997,2066,2150,2220,2296,2391,2490,2555,2619,2672,2730,2778,2839,2903,2970,3032,3098,3160,3217,3281,3346,3412,3464,3524,3598,3672", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "280,467,647,736,829,904,989,1075,1150,1216,1301,1387,1455,1517,1577,1646,1763,1875,1992,2061,2145,2215,2291,2386,2485,2550,2614,2667,2725,2773,2834,2898,2965,3027,3093,3155,3212,3276,3341,3407,3459,3519,3593,3667,3724"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,567,5159,5248,5341,5416,5501,5587,5662,5728,5813,5899,5967,6029,6089,6158,6275,6387,6504,6573,6657,6727,6803,6898,6997,7062,7802,7855,7913,7961,8022,8086,8153,8215,8281,8343,8400,8464,8529,8595,8647,8707,8781,8855", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,64,65,51,59,73,73,56", "endOffsets": "375,562,742,5243,5336,5411,5496,5582,5657,5723,5808,5894,5962,6024,6084,6153,6270,6382,6499,6568,6652,6722,6798,6893,6992,7057,7121,7850,7908,7956,8017,8081,8148,8210,8276,8338,8395,8459,8524,8590,8642,8702,8776,8850,8907"}}]}]}