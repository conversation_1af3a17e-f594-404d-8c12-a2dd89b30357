package com.musicplayer.pro.fragments

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.card.MaterialCardView
import com.google.android.material.switchmaterial.SwitchMaterial
import com.musicplayer.pro.R
import com.musicplayer.pro.themes.ThemeInfo
import com.musicplayer.pro.themes.ThemeManager

/**
 * Fragment إعدادات الثيمات المتقدم
 */
class ThemeSettingsFragment : Fragment() {
    
    private lateinit var themeManager: ThemeManager
    private lateinit var adapter: ThemeAdapter
    
    // Views
    private lateinit var recyclerView: RecyclerView
    private lateinit var dynamicColorsSwitch: SwitchMaterial
    private lateinit var followSystemSwitch: SwitchMaterial
    private lateinit var currentThemeCard: MaterialCardView
    private lateinit var currentThemeName: TextView
    private lateinit var currentThemeDescription: TextView
    private lateinit var currentThemeColor: View
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_theme_settings, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        setupThemeManager()
        setupRecyclerView()
        setupSwitches()
        updateCurrentThemeDisplay()
    }
    
    /**
     * تهيئة Views
     */
    private fun initViews(view: View) {
        recyclerView = view.findViewById(R.id.themesRecyclerView)
        dynamicColorsSwitch = view.findViewById(R.id.dynamicColorsSwitch)
        followSystemSwitch = view.findViewById(R.id.followSystemSwitch)
        currentThemeCard = view.findViewById(R.id.currentThemeCard)
        currentThemeName = view.findViewById(R.id.currentThemeName)
        currentThemeDescription = view.findViewById(R.id.currentThemeDescription)
        currentThemeColor = view.findViewById(R.id.currentThemeColor)
    }
    
    /**
     * إعداد مدير الثيمات
     */
    private fun setupThemeManager() {
        themeManager = ThemeManager.getInstance(requireContext())
    }
    
    /**
     * إعداد RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = ThemeAdapter(themeManager.getAvailableThemes()) { themeInfo ->
            applyTheme(themeInfo)
        }
        
        recyclerView.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = <EMAIL>
        }
    }
    
    /**
     * إعداد المفاتيح
     */
    private fun setupSwitches() {
        // Dynamic Colors Switch
        dynamicColorsSwitch.isChecked = themeManager.isDynamicColorsEnabled()
        dynamicColorsSwitch.setOnCheckedChangeListener { _, isChecked ->
            themeManager.setDynamicColors(isChecked)
            showToast(if (isChecked) "تم تفعيل الألوان الديناميكية" else "تم إلغاء الألوان الديناميكية")
        }
        
        // Follow System Switch
        followSystemSwitch.isChecked = themeManager.isFollowSystemEnabled()
        followSystemSwitch.setOnCheckedChangeListener { _, isChecked ->
            themeManager.setFollowSystem(isChecked)
            showToast(if (isChecked) "سيتبع التطبيق إعدادات النظام" else "لن يتبع التطبيق إعدادات النظام")
        }
    }
    
    /**
     * تطبيق ثيم جديد
     */
    private fun applyTheme(themeInfo: ThemeInfo) {
        try {
            val activity = requireActivity() as AppCompatActivity
            themeManager.recreateWithTheme(activity, themeInfo.id)
            
            android.util.Log.d("ThemeSettingsFragment", "تم تطبيق الثيم: ${themeInfo.name}")
            
        } catch (e: Exception) {
            android.util.Log.e("ThemeSettingsFragment", "خطأ في تطبيق الثيم: ${e.message}")
            showToast("خطأ في تطبيق الثيم")
        }
    }
    
    /**
     * تحديث عرض الثيم الحالي
     */
    private fun updateCurrentThemeDisplay() {
        val currentTheme = themeManager.getCurrentThemeInfo()
        
        currentThemeName.text = currentTheme.name
        currentThemeDescription.text = currentTheme.description
        
        try {
            val color = Color.parseColor(currentTheme.primaryColor)
            currentThemeColor.setBackgroundColor(color)
        } catch (e: Exception) {
            currentThemeColor.setBackgroundColor(Color.GRAY)
        }
        
        // تحديث الـ adapter
        adapter.updateSelectedTheme(currentTheme.id)
    }
    
    /**
     * عرض Toast
     */
    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }
    
    override fun onResume() {
        super.onResume()
        updateCurrentThemeDisplay()
    }
}

/**
 * Adapter للثيمات
 */
class ThemeAdapter(
    private val themes: List<ThemeInfo>,
    private val onThemeClick: (ThemeInfo) -> Unit
) : RecyclerView.Adapter<ThemeAdapter.ThemeViewHolder>() {
    
    private var selectedThemeId: String = ""
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ThemeViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_theme, parent, false)
        return ThemeViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ThemeViewHolder, position: Int) {
        holder.bind(themes[position])
    }
    
    override fun getItemCount(): Int = themes.size
    
    fun updateSelectedTheme(themeId: String) {
        selectedThemeId = themeId
        notifyDataSetChanged()
    }
    
    inner class ThemeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val card: MaterialCardView = itemView.findViewById(R.id.themeCard)
        private val name: TextView = itemView.findViewById(R.id.themeName)
        private val description: TextView = itemView.findViewById(R.id.themeDescription)
        private val colorPreview: View = itemView.findViewById(R.id.themeColorPreview)
        private val selectedIndicator: ImageView = itemView.findViewById(R.id.selectedIndicator)
        
        fun bind(theme: ThemeInfo) {
            name.text = theme.name
            description.text = theme.description
            
            // تطبيق لون المعاينة
            try {
                val color = Color.parseColor(theme.primaryColor)
                colorPreview.setBackgroundColor(color)
            } catch (e: Exception) {
                colorPreview.setBackgroundColor(Color.GRAY)
            }
            
            // إظهار مؤشر التحديد
            selectedIndicator.visibility = if (theme.id == selectedThemeId) {
                View.VISIBLE
            } else {
                View.GONE
            }
            
            // تحديد الكارد المختار
            card.strokeWidth = if (theme.id == selectedThemeId) 4 else 0
            
            // مستمع النقر
            card.setOnClickListener {
                onThemeClick(theme)
            }
        }
    }
}
