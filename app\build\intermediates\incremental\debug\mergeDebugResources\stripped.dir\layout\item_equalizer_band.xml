<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:background="@color/surface_primary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- تردد النطاق -->
        <TextView
            android:id="@+id/frequencyLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="1 kHz" />

        <!-- مستوى النطاق -->
        <TextView
            android:id="@+id/levelLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            tools:text="0.0 dB" />

        <!-- شريط التحكم -->
        <SeekBar
            android:id="@+id/levelSeekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:max="100"
            android:progress="50"
            android:progressTint="@color/accent_primary"
            android:thumbTint="@color/accent_primary" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
