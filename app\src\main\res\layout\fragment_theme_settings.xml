<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorBackground"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- عنوان الصفحة -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="إعدادات الثيمات"
            android:textAppearance="?attr/textAppearanceHeadlineMedium"
            android:textColor="?attr/colorOnBackground"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- الثيم الحالي -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/currentThemeCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="?attr/colorSurface">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp">

                <!-- معاينة اللون -->
                <View
                    android:id="@+id/currentThemeColor"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/color_preview_circle" />

                <!-- معلومات الثيم -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الثيم الحالي"
                        android:textAppearance="?attr/textAppearanceLabelMedium"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/currentThemeName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="حديث"
                        android:textAppearance="?attr/textAppearanceTitleLarge"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="2dp" />

                    <TextView
                        android:id="@+id/currentThemeDescription"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تصميم Material Design 3 الحديث"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- إعدادات الثيم -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="?attr/colorSurface">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="إعدادات متقدمة"
                    android:textAppearance="?attr/textAppearanceTitleMedium"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <!-- الألوان الديناميكية -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الألوان الديناميكية"
                            android:textAppearance="?attr/textAppearanceBodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="استخدام ألوان من خلفية النظام"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/dynamicColorsSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- متابعة النظام -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="متابعة النظام"
                            android:textAppearance="?attr/textAppearanceBodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تغيير الثيم تلقائياً مع النظام"
                            android:textAppearance="?attr/textAppearanceBodySmall"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/followSystemSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- عنوان الثيمات -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="اختر الثيم"
            android:textAppearance="?attr/textAppearanceTitleLarge"
            android:textColor="?attr/colorOnBackground"
            android:layout_marginBottom="16dp" />

        <!-- قائمة الثيمات -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/themesRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            tools:listitem="@layout/item_theme" />

    </LinearLayout>

</ScrollView>
