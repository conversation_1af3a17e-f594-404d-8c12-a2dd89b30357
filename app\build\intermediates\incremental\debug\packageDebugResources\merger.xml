<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\python\PythonProject1\kotlin_version\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\python\PythonProject1\kotlin_version\app\src\main\res"><file name="default_album_cover" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\default_album_cover.xml" qualifiers="" type="drawable"/><file name="ic_add" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_download" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_download.xml" qualifiers="" type="drawable"/><file name="ic_favorite_filled" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_favorite_filled.xml" qualifiers="" type="drawable"/><file name="ic_favorite_outline" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_favorite_outline.xml" qualifiers="" type="drawable"/><file name="ic_link" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_link.xml" qualifiers="" type="drawable"/><file name="ic_loading" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_loading.xml" qualifiers="" type="drawable"/><file name="ic_music_library" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_music_library.xml" qualifiers="" type="drawable"/><file name="ic_now_playing" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_now_playing.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_play" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="ic_playlist_add" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_playlist_add.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_repeat_all" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_repeat_all.xml" qualifiers="" type="drawable"/><file name="ic_repeat_off" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_repeat_off.xml" qualifiers="" type="drawable"/><file name="ic_repeat_one" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_repeat_one.xml" qualifiers="" type="drawable"/><file name="ic_search" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_share" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_share.xml" qualifiers="" type="drawable"/><file name="ic_shuffle_off" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_shuffle_off.xml" qualifiers="" type="drawable"/><file name="ic_shuffle_on" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_shuffle_on.xml" qualifiers="" type="drawable"/><file name="ic_skip_next" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_skip_next.xml" qualifiers="" type="drawable"/><file name="ic_skip_previous" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_skip_previous.xml" qualifiers="" type="drawable"/><file name="spinner_background" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="bottom_music_bar" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\bottom_music_bar.xml" qualifiers="" type="layout"/><file name="dialog_add_download" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\dialog_add_download.xml" qualifiers="" type="layout"/><file name="fragment_download" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\fragment_download.xml" qualifiers="" type="layout"/><file name="fragment_main" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\fragment_main.xml" qualifiers="" type="layout"/><file name="fragment_now_playing" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\fragment_now_playing.xml" qualifiers="" type="layout"/><file name="item_download" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\item_download.xml" qualifiers="" type="layout"/><file name="item_song" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\item_song.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file path="E:\python\PythonProject1\kotlin_version\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="accent_primary">#FF6200EE</color><color name="accent_secondary">#FF3700B3</color><color name="background_primary">#FFFFFF</color><color name="background_secondary">#F5F5F5</color><color name="surface_primary">#FFFFFF</color><color name="surface_secondary">#F8F9FA</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color><color name="text_tertiary">#BDBDBD</color><color name="text_on_accent">#FFFFFF</color><color name="success">#4CAF50</color><color name="warning">#FF9800</color><color name="error">#F44336</color><color name="info">#2196F3</color><color name="background_primary_dark">#121212</color><color name="background_secondary_dark">#1E1E1E</color><color name="surface_primary_dark">#1E1E1E</color><color name="surface_secondary_dark">#2D2D2D</color><color name="text_primary_dark">#FFFFFF</color><color name="text_secondary_dark">#B3B3B3</color><color name="text_tertiary_dark">#666666</color><color name="transparent">#00000000</color><color name="semi_transparent_black">#80000000</color><color name="semi_transparent_white">#80FFFFFF</color><color name="surface_tertiary">#E0E0E0</color></file><file path="E:\python\PythonProject1\kotlin_version\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Music Player Pro</string><string name="main_screen">الرئيسية</string><string name="now_playing">قيد التشغيل</string><string name="downloads">التحميلات</string><string name="playlists">قوائم التشغيل</string><string name="favorites">المفضلة</string><string name="settings">الإعدادات</string><string name="play">تشغيل</string><string name="pause">إيقاف مؤقت</string><string name="play_pause">تشغيل/إيقاف</string><string name="next">التالي</string><string name="previous">السابق</string><string name="shuffle">تشغيل عشوائي</string><string name="shuffle_all">تشغيل الكل عشوائياً</string><string name="repeat">تكرار</string><string name="repeat_off">إيقاف التكرار</string><string name="repeat_one">تكرار الأغنية</string><string name="repeat_all">تكرار الكل</string><string name="favorite">مفضلة</string><string name="add_to_playlist">إضافة إلى قائمة التشغيل</string><string name="share">مشاركة</string><string name="delete">حذف</string><string name="song_details">تفاصيل الأغنية</string><string name="search">بحث</string><string name="search_songs">البحث في الأغاني...</string><string name="search_artists">البحث في الفنانين...</string><string name="search_albums">البحث في الألبومات...</string><string name="no_songs_found">لا توجد أغاني</string><string name="no_song_playing">لا توجد أغنية قيد التشغيل</string><string name="add_songs_to_get_started">أضف أغاني للبدء</string><string name="no_downloads">لا توجد تحميلات</string><string name="no_playlists">لا توجد قوائم تشغيل</string><string name="download">تحميل</string><string name="downloading">جاري التحميل</string><string name="download_completed">اكتمل التحميل</string><string name="download_failed">فشل التحميل</string><string name="download_paused">تم إيقاف التحميل</string><string name="download_cancelled">تم إلغاء التحميل</string><string name="add_download">إضافة تحميل</string><string name="url_required">الرابط مطلوب</string><string name="invalid_url">رابط غير صحيح</string><string name="permissions_required">الأذونات مطلوبة</string><string name="permissions_required_message">يحتاج التطبيق إلى أذونات للوصول إلى ملفات الموسيقى</string><string name="grant_permissions">منح الأذونات</string><string name="loading">جاري التحميل...</string><string name="ok">موافق</string><string name="cancel">إلغاء</string><string name="yes">نعم</string><string name="no">لا</string><string name="exit">خروج</string><string name="error">خطأ</string><string name="success">نجح</string><string name="warning">تحذير</string><string name="info">معلومات</string><string formatted="false" name="time_format">%d:%02d</string><string name="unknown_artist">فنان غير معروف</string><string name="unknown_album">ألبوم غير معروف</string><string name="backup_data_summary">إنشاء نسخة احتياطية من قوائم التشغيل والإعدادات</string><string name="storage_settings">إعدادات التخزين</string><string name="about_app">حول التطبيق</string><string name="audio_effects_summary">تفعيل التأثيرات الصوتية المتقدمة</string><string name="restore_data">استعادة البيانات</string><string name="virtualizer_summary">محاكاة الصوت المحيطي</string><string name="clear_cache">مسح الذاكرة المؤقتة</string><string name="license">الترخيص</string><string name="theme">الثيم</string><string name="version">الإصدار</string><string name="appearance_settings">إعدادات المظهر</string><string name="audio_quality">جودة الصوت</string><string name="language_summary">اختر لغة التطبيق</string><string name="theme_summary">اختر مظهر التطبيق</string><string name="auto_download_thumbnails_summary">تحميل صور الأغلاف مع الملفات</string><string name="bass_boost">تحسين الجهير</string><string name="license_summary">عرض معلومات الترخيص</string><string name="virtualizer">الصوت المحيطي</string><string name="reset_settings">إعادة تعيين الإعدادات</string><string name="download_settings">إعدادات التحميل</string><string name="download_folder_summary">اختر مكان حفظ التحميلات</string><string name="wifi_only_downloads">التحميل عبر WiFi فقط</string><string name="reset_settings_summary">إعادة جميع الإعدادات إلى القيم الافتراضية</string><string name="audio_settings">إعدادات الصوت</string><string name="download_folder">مجلد التحميل</string><string name="about_message">تطبيق مشغل موسيقى متقدم مع ميزات التحميل والتأثيرات الصوتية المتطورة.</string><string name="share_app">مشاركة التطبيق</string><string name="equalizer_summary">ضبط ترددات الصوت</string><string name="equalizer">المعادل الصوتي</string><string name="backup_data">نسخ احتياطي للبيانات</string><string name="default_download_quality_summary">الجودة المستخدمة افتراضياً للتحميلات</string><string name="default_download_format">تنسيق التحميل الافتراضي</string><string name="restore_data_summary">استعادة البيانات من النسخة الاحتياطية</string><string name="share_app_summary">شارك التطبيق مع الأصدقاء</string><string name="bass_boost_summary">تعزيز الترددات المنخفضة</string><string name="rate_app">تقييم التطبيق</string><string name="rate_app_summary">قيم التطبيق في متجر Google Play</string><string name="auto_download_thumbnails">تحميل الصور المصغرة تلقائياً</string><string name="about">حول</string><string name="clear_cache_summary">حذف الملفات المؤقتة لتوفير مساحة</string><string name="license_text">هذا التطبيق مرخص تحت رخصة MIT.\n\nيُسمح بالاستخدام والتعديل والتوزيع مع الاحتفاظ بحقوق المؤلف.</string><string name="language">اللغة</string><string name="audio_quality_summary">اختر جودة تشغيل الصوت</string><string name="check_updates_summary">البحث عن إصدارات جديدة</string><string name="default_download_format_summary">التنسيق المستخدم افتراضياً للتحميلات</string><string name="app_info">معلومات التطبيق</string><string name="audio_effects">التأثيرات الصوتية</string><string name="check_updates">التحقق من التحديثات</string><string name="default_download_quality">جودة التحميل الافتراضية</string><string name="wifi_only_downloads_summary">منع التحميل عبر بيانات الهاتف</string><string name="equalizer_bands">نطاقات المعادل</string><string name="permissions_explanation">يحتاج التطبيق إلى الأذونات الأساسية التالية للعمل:</string><string name="open_settings">فتح الإعدادات</string><string name="permissions_denied_message">لا يمكن للتطبيق العمل بدون الأذونات المطلوبة. يرجى منح الأذونات من الإعدادات.</string><string name="permissions_denied">تم رفض الأذونات</string><string name="no_songs_found_scan">لم يتم العثور على أغاني</string><string name="songs_found">تم العثور على %d أغنية</string><string name="scan_complete">تم الانتهاء من الفحص</string><string name="scanning_music">جاري البحث عن الأغاني...</string><string name="scan_music">فحص الأغاني</string><string name="album_cover">غلاف الألبوم</string><string name="remove_from_favorites">إزالة من المفضلة</string><string name="add_to_favorites">إضافة للمفضلة</string></file><file path="E:\python\PythonProject1\kotlin_version\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MusicPlayerPro" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style><style name="Theme.MusicPlayerPro.Splash" parent="Theme.MusicPlayerPro">
        <item name="android:windowBackground">@color/purple_500</item>
    </style></file><file name="automotive_app_desc" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\xml\automotive_app_desc.xml" qualifiers="" type="xml"/><file name="backup_rules" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="ic_music_note" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_music_note.xml" qualifiers="" type="drawable"/><file name="ic_audio" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_audio.xml" qualifiers="" type="drawable"/><file name="ic_equalizer" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_equalizer.xml" qualifiers="" type="drawable"/><file name="ic_exit" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_exit.xml" qualifiers="" type="drawable"/><file name="ic_info" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_language" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_language.xml" qualifiers="" type="drawable"/><file name="ic_palette" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_palette.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_storage" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_storage.xml" qualifiers="" type="drawable"/><file name="ic_theme" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_theme.xml" qualifiers="" type="drawable"/><file name="activity_settings" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="main_menu" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\menu\main_menu.xml" qualifiers="" type="menu"/><file path="E:\python\PythonProject1\kotlin_version\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="theme_entries">
        <item>فاتح</item>
        <item>داكن</item>
        <item>تلقائي (حسب النظام)</item>
    </string-array><string-array name="theme_values">
        <item>light</item>
        <item>dark</item>
        <item>auto</item>
    </string-array><string-array name="language_entries">
        <item>العربية</item>
        <item>English</item>
        <item>تلقائي (حسب النظام)</item>
    </string-array><string-array name="language_values">
        <item>ar</item>
        <item>en</item>
        <item>auto</item>
    </string-array><string-array name="audio_quality_entries">
        <item>منخفضة (128 kbps)</item>
        <item>متوسطة (192 kbps)</item>
        <item>عالية (320 kbps)</item>
        <item>بدون ضغط (FLAC)</item>
    </string-array><string-array name="audio_quality_values">
        <item>low</item>
        <item>medium</item>
        <item>high</item>
        <item>lossless</item>
    </string-array><string-array name="download_quality_entries">
        <item>أفضل جودة متاحة</item>
        <item>1080p</item>
        <item>720p</item>
        <item>480p</item>
        <item>360p</item>
        <item>240p</item>
        <item>صوت فقط</item>
    </string-array><string-array name="download_quality_values">
        <item>best</item>
        <item>1080p</item>
        <item>720p</item>
        <item>480p</item>
        <item>360p</item>
        <item>240p</item>
        <item>audio</item>
    </string-array><string-array name="download_format_entries">
        <item>MP3</item>
        <item>MP4</item>
        <item>M4A</item>
        <item>WEBM</item>
        <item>FLAC</item>
        <item>WAV</item>
    </string-array><string-array name="download_format_values">
        <item>mp3</item>
        <item>mp4</item>
        <item>m4a</item>
        <item>webm</item>
        <item>flac</item>
        <item>wav</item>
    </string-array></file><file name="preferences" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\xml\preferences.xml" qualifiers="" type="xml"/><file name="ic_audio_effects" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_audio_effects.xml" qualifiers="" type="drawable"/><file name="ic_backup" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_backup.xml" qualifiers="" type="drawable"/><file name="ic_bass" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_bass.xml" qualifiers="" type="drawable"/><file name="ic_clear" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_clear.xml" qualifiers="" type="drawable"/><file name="ic_download_quality" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_download_quality.xml" qualifiers="" type="drawable"/><file name="ic_file_format" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_file_format.xml" qualifiers="" type="drawable"/><file name="ic_folder" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_folder.xml" qualifiers="" type="drawable"/><file name="ic_high_quality" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_high_quality.xml" qualifiers="" type="drawable"/><file name="ic_image" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_image.xml" qualifiers="" type="drawable"/><file name="ic_license" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_license.xml" qualifiers="" type="drawable"/><file name="ic_reset" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_reset.xml" qualifiers="" type="drawable"/><file name="ic_restore" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_restore.xml" qualifiers="" type="drawable"/><file name="ic_star" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_star.xml" qualifiers="" type="drawable"/><file name="ic_surround_sound" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_surround_sound.xml" qualifiers="" type="drawable"/><file name="ic_update" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_update.xml" qualifiers="" type="drawable"/><file name="ic_wifi" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_wifi.xml" qualifiers="" type="drawable"/><file name="activity_equalizer" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\activity_equalizer.xml" qualifiers="" type="layout"/><file name="item_equalizer_band" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\item_equalizer_band.xml" qualifiers="" type="layout"/><file name="ic_warning" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\ic_warning.xml" qualifiers="" type="drawable"/><file name="bottom_bar_background" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\bottom_bar_background.xml" qualifiers="" type="drawable"/><file name="control_button_background" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\control_button_background.xml" qualifiers="" type="drawable"/><file name="play_button_background" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\play_button_background.xml" qualifiers="" type="drawable"/><file name="playing_overlay" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\playing_overlay.xml" qualifiers="" type="drawable"/><file name="quality_badge" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\quality_badge.xml" qualifiers="" type="drawable"/><file name="bg_quality_badge" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\bg_quality_badge.xml" qualifiers="" type="drawable"/><file name="bg_thumbnail" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\drawable\bg_thumbnail.xml" qualifiers="" type="drawable"/><file name="fragment_advanced_download" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\fragment_advanced_download.xml" qualifiers="" type="layout"/><file name="item_advanced_download" path="E:\python\PythonProject1\kotlin_version\app\src\main\res\layout\item_advanced_download.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\python\PythonProject1\kotlin_version\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\python\PythonProject1\kotlin_version\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\python\PythonProject1\kotlin_version\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\python\PythonProject1\kotlin_version\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>