(androidx.appcompat.app.AppCompatActivityandroidx.fragment.app.Fragment1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderkotlin.Enumandroid.os.Parcelable#androidx.lifecycle.AndroidViewModel0androidx.viewpager2.adapter.FragmentStateAdapterandroid.app.Application,androidx.preference.PreferenceFragmentCompatBandroid.content.SharedPreferences.OnSharedPreferenceChangeListener(android.database.sqlite.SQLiteOpenHelperandroid.app.Serviceandroid.os.Binder                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   