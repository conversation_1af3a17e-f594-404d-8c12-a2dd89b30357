(androidx.appcompat.app.AppCompatActivity1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderandroidx.fragment.app.Fragmentkotlin.Enumandroid.os.Parcelable#androidx.lifecycle.AndroidViewModel0androidx.viewpager2.adapter.FragmentStateAdapterandroid.app.Application,androidx.preference.PreferenceFragmentCompatBandroid.content.SharedPreferences.OnSharedPreferenceChangeListener(android.database.sqlite.SQLiteOpenHelperandroid.app.Serviceandroid.os.Binder                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   