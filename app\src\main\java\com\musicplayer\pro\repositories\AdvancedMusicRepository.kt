package com.musicplayer.pro.repositories

import android.content.Context
import android.provider.MediaStore
import androidx.lifecycle.LiveData
import com.musicplayer.pro.database.MusicDatabase
import com.musicplayer.pro.database.SongDao
import com.musicplayer.pro.models.Song
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.withContext

/**
 * مستودع الموسيقى المتقدم - يجمع بين قاعدة البيانات المحلية و MediaStore
 */
class AdvancedMusicRepository(
    private val context: Context,
    private val database: MusicDatabase
) {
    
    private val songDao: SongDao = database.songDao()
    
    /**
     * الحصول على جميع الأغاني (محلية + محملة)
     */
    fun getAllSongs(): Flow<List<Song>> {
        return combine(
            getSystemSongs(),
            songDao.getAllSongs()
        ) { systemSongs, downloadedSongs ->
            // دمج الأغاني مع تجنب التكرار
            val allSongs = mutableListOf<Song>()
            allSongs.addAll(systemSongs)
            
            // إضافة الأغاني المحملة التي ليست موجودة في النظام
            downloadedSongs.forEach { downloadedSong ->
                if (!systemSongs.any { it.path == downloadedSong.path }) {
                    allSongs.add(downloadedSong)
                }
            }
            
            allSongs.sortedBy { it.title }
        }
    }
    
    /**
     * الحصول على الأغاني من النظام
     */
    private fun getSystemSongs(): Flow<List<Song>> {
        return kotlinx.coroutines.flow.flow {
            emit(scanSystemSongs())
        }
    }
    
    /**
     * مسح أغاني النظام
     */
    private suspend fun scanSystemSongs(): List<Song> {
        return withContext(Dispatchers.IO) {
            val songs = mutableListOf<Song>()
            
            try {
                val projection = arrayOf(
                    MediaStore.Audio.Media._ID,
                    MediaStore.Audio.Media.TITLE,
                    MediaStore.Audio.Media.ARTIST,
                    MediaStore.Audio.Media.ALBUM,
                    MediaStore.Audio.Media.DURATION,
                    MediaStore.Audio.Media.DATA,
                    MediaStore.Audio.Media.SIZE,
                    MediaStore.Audio.Media.YEAR,
                    MediaStore.Audio.Media.TRACK,
                    MediaStore.Audio.Media.MIME_TYPE,
                    MediaStore.Audio.Media.DATE_ADDED,
                    MediaStore.Audio.Media.DATE_MODIFIED
                )
                
                val selection = "${MediaStore.Audio.Media.IS_MUSIC} = 1"
                val sortOrder = "${MediaStore.Audio.Media.TITLE} ASC"
                
                val cursor = context.contentResolver.query(
                    MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                    projection,
                    selection,
                    null,
                    sortOrder
                )
                
                cursor?.use {
                    val idColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media._ID)
                    val titleColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.TITLE)
                    val artistColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ARTIST)
                    val albumColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.ALBUM)
                    val durationColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION)
                    val dataColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)
                    val sizeColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE)
                    val yearColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.YEAR)
                    val trackColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.TRACK)
                    val mimeTypeColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.MIME_TYPE)
                    val dateAddedColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_ADDED)
                    val dateModifiedColumn = it.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_MODIFIED)
                    
                    while (it.moveToNext()) {
                        val id = it.getLong(idColumn)
                        val title = it.getString(titleColumn) ?: "Unknown Title"
                        val artist = it.getString(artistColumn) ?: "Unknown Artist"
                        val album = it.getString(albumColumn) ?: "Unknown Album"
                        val duration = it.getLong(durationColumn)
                        val path = it.getString(dataColumn) ?: ""
                        val size = it.getLong(sizeColumn)
                        val year = it.getInt(yearColumn)
                        val track = it.getInt(trackColumn)
                        val mimeType = it.getString(mimeTypeColumn) ?: ""
                        val dateAdded = it.getLong(dateAddedColumn) * 1000 // تحويل إلى milliseconds
                        val dateModified = it.getLong(dateModifiedColumn) * 1000
                        
                        val song = Song(
                            id = id,
                            title = title,
                            artist = artist,
                            album = album,
                            duration = duration,
                            path = path,
                            size = size,
                            year = year,
                            track = track,
                            mimeType = mimeType,
                            dateAdded = dateAdded,
                            dateModified = dateModified,
                            isFromDownload = false
                        )
                        
                        songs.add(song)
                    }
                }
                
                android.util.Log.d("AdvancedMusicRepository", "تم جلب ${songs.size} أغنية من النظام")
                
            } catch (e: Exception) {
                android.util.Log.e("AdvancedMusicRepository", "خطأ في جلب أغاني النظام: ${e.message}")
            }
            
            songs
        }
    }
    
    /**
     * الحصول على الأغاني المحملة فقط
     */
    fun getDownloadedSongs(): Flow<List<Song>> {
        return songDao.getDownloadedSongs()
    }
    
    /**
     * البحث في الأغاني
     */
    fun searchSongs(query: String): Flow<List<Song>> {
        return combine(
            getSystemSongs(),
            songDao.searchSongs(query)
        ) { systemSongs, downloadedSongs ->
            val searchResults = mutableListOf<Song>()
            
            // البحث في أغاني النظام
            systemSongs.filter { song ->
                song.title.contains(query, ignoreCase = true) ||
                song.artist.contains(query, ignoreCase = true) ||
                song.album.contains(query, ignoreCase = true)
            }.let { searchResults.addAll(it) }
            
            // إضافة نتائج البحث من الأغاني المحملة
            downloadedSongs.forEach { downloadedSong ->
                if (!searchResults.any { it.path == downloadedSong.path }) {
                    searchResults.add(downloadedSong)
                }
            }
            
            searchResults.sortedBy { it.title }
        }
    }
    
    /**
     * الحصول على الأغاني المفضلة
     */
    fun getFavoriteSongs(): Flow<List<Song>> {
        return songDao.getFavoriteSongs()
    }
    
    /**
     * الحصول على الأغاني الأكثر تشغيلاً
     */
    fun getMostPlayedSongs(limit: Int = 20): Flow<List<Song>> {
        return songDao.getMostPlayedSongs(limit)
    }
    
    /**
     * الحصول على الأغاني المضافة حديثاً
     */
    fun getRecentlyAddedSongs(limit: Int = 20): Flow<List<Song>> {
        return songDao.getRecentlyAddedSongs(limit)
    }
    
    /**
     * إضافة أغنية محملة
     */
    suspend fun addDownloadedSong(song: Song): Long {
        return songDao.insertSong(song.copy(isFromDownload = true))
    }
    
    /**
     * تحديث أغنية
     */
    suspend fun updateSong(song: Song) {
        songDao.updateSong(song)
    }
    
    /**
     * حذف أغنية
     */
    suspend fun deleteSong(song: Song) {
        songDao.deleteSong(song)
    }
    
    /**
     * تحديث عدد مرات التشغيل
     */
    suspend fun incrementPlayCount(songId: Long) {
        songDao.incrementPlayCount(songId)
    }
    
    /**
     * تحديث حالة المفضلة
     */
    suspend fun updateFavoriteStatus(songId: Long, isFavorite: Boolean) {
        songDao.updateFavoriteStatus(songId, isFavorite)
    }
    
    /**
     * تحديث التقييم
     */
    suspend fun updateRating(songId: Long, rating: Float) {
        songDao.updateRating(songId, rating)
    }
    
    /**
     * الحصول على إحصائيات
     */
    suspend fun getStatistics(): MusicStatistics {
        return withContext(Dispatchers.IO) {
            val totalSongs = songDao.getTotalSongsCount()
            val downloadedSongs = songDao.getDownloadedSongsCount()
            val totalDuration = songDao.getTotalDuration()
            val totalSize = songDao.getTotalSize()
            
            MusicStatistics(
                totalSongs = totalSongs,
                downloadedSongs = downloadedSongs,
                systemSongs = totalSongs - downloadedSongs,
                totalDuration = totalDuration,
                totalSize = totalSize
            )
        }
    }
    
    /**
     * تحديث مكتبة الأغاني
     */
    suspend fun refreshLibrary() {
        withContext(Dispatchers.IO) {
            // إعادة مسح أغاني النظام
            scanSystemSongs()
        }
    }
}

/**
 * إحصائيات المكتبة الموسيقية
 */
data class MusicStatistics(
    val totalSongs: Int,
    val downloadedSongs: Int,
    val systemSongs: Int,
    val totalDuration: Long,
    val totalSize: Long
)
