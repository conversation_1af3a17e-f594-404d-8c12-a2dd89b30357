package com.musicplayer.pro.fragments

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.google.android.material.card.MaterialCardView
import com.musicplayer.pro.R

/**
 * Fragment الإعدادات
 */
class SettingsFragment : Fragment() {
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_settings, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupSettingsItems(view)
    }
    
    /**
     * إعداد عناصر الإعدادات
     */
    private fun setupSettingsItems(view: View) {
        // إعدادات الثيمات
        val themeSettingsCard: MaterialCardView = view.findViewById(R.id.themeSettingsCard)
        themeSettingsCard.setOnClickListener {
            openThemeSettings()
        }
        
        // إعدادات أخرى يمكن إضافتها لاحقاً
    }
    
    /**
     * فتح إعدادات الثيمات
     */
    private fun openThemeSettings() {
        try {
            val fragment = ThemeSettingsFragment()
            // فتح إعدادات الثيمات في نشاط منفصل أو استبدال Fragment الحالي
            val activity = requireActivity()
            if (activity is androidx.appcompat.app.AppCompatActivity) {
                val fragment = ThemeSettingsFragment()
                activity.supportFragmentManager.beginTransaction()
                    .replace(android.R.id.content, fragment)
                    .addToBackStack(null)
                    .commit()
            }
        } catch (e: Exception) {
            android.util.Log.e("SettingsFragment", "خطأ في فتح إعدادات الثيمات: ${e.message}")
        }
    }
}
