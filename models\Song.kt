package com.musicplayer.pro.models

import android.graphics.Bitmap
import android.net.Uri
import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.Ignore
import kotlinx.parcelize.Parcelize
import java.io.Serializable

/**
 * نموذج بيانات الأغنية
 * يحتوي على جميع المعلومات المتعلقة بالأغنية
 */
@Entity(tableName = "songs")
@Parcelize
data class Song(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0L,
    val title: String = "",
    val artist: String = "",
    val album: String = "",
    val duration: Long = 0L,
    val path: String = "",
    @Ignore val uri: Uri? = null,
    val albumId: Long = 0L,
    val artistId: Long = 0L,
    val track: Int = 0,
    val year: Int = 0,
    val genre: String = "",
    val size: Long = 0L,
    val mimeType: String = "",
    val bitrate: Int = 0,
    val sampleRate: Int = 0,
    val channels: Int = 0,
    val dateAdded: Long = 0L,
    val dateModified: Long = 0L,
    val isFromDownload: Boolean = false,
    val downloadUrl: String = "",
    val lyrics: String = "",
    val isFavorite: Boolean = false,
    val playCount: Int = 0,
    val lastPlayed: Long = 0L,
    val rating: Float = 0f,
    val albumArtPath: String = "",
    @Ignore val albumArt: Bitmap? = null
) : Parcelable {
    
    /**
     * الحصول على اسم الفنان المنسق
     */
    fun getFormattedArtist(): String {
        return when {
            artist.isBlank() -> "فنان غير معروف"
            artist.contains("unknown", ignoreCase = true) -> "فنان غير معروف"
            else -> artist
        }
    }
    
    /**
     * الحصول على اسم الألبوم المنسق
     */
    fun getFormattedAlbum(): String {
        return when {
            album.isBlank() -> "ألبوم غير معروف"
            album.contains("unknown", ignoreCase = true) -> "ألبوم غير معروف"
            else -> album
        }
    }
    
    /**
     * الحصول على عنوان الأغنية المنسق
     */
    fun getFormattedTitle(): String {
        return when {
            title.isBlank() -> "أغنية غير معروفة"
            else -> title
        }
    }
    
    /**
     * الحصول على مدة الأغنية بصيغة منسقة (mm:ss)
     */
    fun getFormattedDuration(): String {
        val minutes = (duration / 1000) / 60
        val seconds = (duration / 1000) % 60
        return String.format("%02d:%02d", minutes, seconds)
    }
    
    /**
     * الحصول على حجم الملف بصيغة منسقة
     */
    fun getFormattedSize(): String {
        return when {
            size < 1024 -> "${size} B"
            size < 1024 * 1024 -> "${size / 1024} KB"
            size < 1024 * 1024 * 1024 -> "${size / (1024 * 1024)} MB"
            else -> "${size / (1024 * 1024 * 1024)} GB"
        }
    }
    
    /**
     * الحصول على معدل البت بصيغة منسقة
     */
    fun getFormattedBitrate(): String {
        return if (bitrate > 0) "${bitrate} kbps" else "غير معروف"
    }
    
    /**
     * الحصول على معدل العينة بصيغة منسقة
     */
    fun getFormattedSampleRate(): String {
        return if (sampleRate > 0) "${sampleRate} Hz" else "غير معروف"
    }
    
    /**
     * الحصول على عدد القنوات بصيغة منسقة
     */
    fun getFormattedChannels(): String {
        return when (channels) {
            1 -> "أحادي"
            2 -> "ستيريو"
            else -> "$channels قنوات"
        }
    }
    
    /**
     * الحصول على نوع الملف
     */
    fun getFileExtension(): String {
        return path.substringAfterLast('.', "").uppercase()
    }
    
    /**
     * التحقق من صحة الأغنية
     */
    fun isValid(): Boolean {
        return path.isNotBlank() && 
               title.isNotBlank() && 
               duration > 0 && 
               java.io.File(path).exists()
    }
    
    /**
     * التحقق من وجود صورة الألبوم
     */
    fun hasAlbumArt(): Boolean {
        return albumArt != null || albumArtPath.isNotBlank()
    }
    
    /**
     * الحصول على معلومات الجودة
     */
    fun getQualityInfo(): String {
        return buildString {
            if (bitrate > 0) append("${bitrate}kbps")
            if (sampleRate > 0) {
                if (isNotEmpty()) append(" • ")
                append("${sampleRate}Hz")
            }
            if (channels > 0) {
                if (isNotEmpty()) append(" • ")
                append(getFormattedChannels())
            }
            if (isEmpty()) append("جودة غير معروفة")
        }
    }
    
    /**
     * الحصول على معلومات مفصلة عن الأغنية
     */
    fun getDetailedInfo(): String {
        return buildString {
            appendLine("العنوان: ${getFormattedTitle()}")
            appendLine("الفنان: ${getFormattedArtist()}")
            appendLine("الألبوم: ${getFormattedAlbum()}")
            appendLine("المدة: ${getFormattedDuration()}")
            appendLine("الحجم: ${getFormattedSize()}")
            appendLine("النوع: ${getFileExtension()}")
            appendLine("الجودة: ${getQualityInfo()}")
            if (year > 0) appendLine("السنة: $year")
            if (genre.isNotBlank()) appendLine("النوع الموسيقي: $genre")
            if (track > 0) appendLine("رقم المقطوعة: $track")
            appendLine("عدد مرات التشغيل: $playCount")
            if (rating > 0) appendLine("التقييم: ${"★".repeat(rating.toInt())}")
        }
    }
    
    /**
     * نسخ الأغنية مع تحديث بعض الخصائص
     */
    fun copyWithUpdates(
        isFavorite: Boolean? = null,
        playCount: Int? = null,
        lastPlayed: Long? = null,
        rating: Float? = null,
        albumArt: Bitmap? = null
    ): Song {
        return copy(
            isFavorite = isFavorite ?: this.isFavorite,
            playCount = playCount ?: this.playCount,
            lastPlayed = lastPlayed ?: this.lastPlayed,
            rating = rating ?: this.rating,
            albumArt = albumArt ?: this.albumArt
        )
    }
    
    /**
     * تحويل إلى خريطة للحفظ في قاعدة البيانات
     */
    fun toMap(): Map<String, Any?> {
        return mapOf(
            "id" to id,
            "title" to title,
            "artist" to artist,
            "album" to album,
            "duration" to duration,
            "path" to path,
            "albumId" to albumId,
            "artistId" to artistId,
            "track" to track,
            "year" to year,
            "genre" to genre,
            "size" to size,
            "mimeType" to mimeType,
            "bitrate" to bitrate,
            "sampleRate" to sampleRate,
            "channels" to channels,
            "dateAdded" to dateAdded,
            "dateModified" to dateModified,
            "isFromDownload" to isFromDownload,
            "downloadUrl" to downloadUrl,
            "lyrics" to lyrics,
            "isFavorite" to isFavorite,
            "playCount" to playCount,
            "lastPlayed" to lastPlayed,
            "rating" to rating,
            "albumArtPath" to albumArtPath
        )
    }
    
    companion object {
        /**
         * إنشاء أغنية من خريطة البيانات
         */
        fun fromMap(map: Map<String, Any?>): Song {
            return Song(
                id = map["id"] as? Long ?: 0L,
                title = map["title"] as? String ?: "",
                artist = map["artist"] as? String ?: "",
                album = map["album"] as? String ?: "",
                duration = map["duration"] as? Long ?: 0L,
                path = map["path"] as? String ?: "",
                albumId = map["albumId"] as? Long ?: 0L,
                artistId = map["artistId"] as? Long ?: 0L,
                track = map["track"] as? Int ?: 0,
                year = map["year"] as? Int ?: 0,
                genre = map["genre"] as? String ?: "",
                size = map["size"] as? Long ?: 0L,
                mimeType = map["mimeType"] as? String ?: "",
                bitrate = map["bitrate"] as? Int ?: 0,
                sampleRate = map["sampleRate"] as? Int ?: 0,
                channels = map["channels"] as? Int ?: 0,
                dateAdded = map["dateAdded"] as? Long ?: 0L,
                dateModified = map["dateModified"] as? Long ?: 0L,
                isFromDownload = map["isFromDownload"] as? Boolean ?: false,
                downloadUrl = map["downloadUrl"] as? String ?: "",
                lyrics = map["lyrics"] as? String ?: "",
                isFavorite = map["isFavorite"] as? Boolean ?: false,
                playCount = map["playCount"] as? Int ?: 0,
                lastPlayed = map["lastPlayed"] as? Long ?: 0L,
                rating = map["rating"] as? Float ?: 0f,
                albumArtPath = map["albumArtPath"] as? String ?: ""
            )
        }
        
        /**
         * إنشاء أغنية فارغة
         */
        fun empty(): Song {
            return Song()
        }
        
        /**
         * إنشاء أغنية تجريبية
         */
        fun sample(): Song {
            return Song(
                id = 1L,
                title = "أغنية تجريبية",
                artist = "فنان تجريبي",
                album = "ألبوم تجريبي",
                duration = 180000L, // 3 دقائق
                path = "/storage/emulated/0/Music/sample.mp3"
            )
        }
    }
}
