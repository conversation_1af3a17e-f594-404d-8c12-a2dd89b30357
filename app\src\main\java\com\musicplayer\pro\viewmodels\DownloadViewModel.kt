package com.musicplayer.pro.viewmodels

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.models.DownloadStatus
import com.musicplayer.pro.managers.DownloadManager
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.util.*

/**
 * ViewModel للتحميلات - مطابق لـ DownloadManager في Python
 */
class DownloadViewModel(application: Application) : AndroidViewModel(application) {
    
    private val downloadManager = DownloadManager(application)
    
    // البيانات المباشرة
    private val _downloads = MutableLiveData<List<Download>>()
    val downloads: LiveData<List<Download>> = _downloads
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage

    private val _message = MutableLiveData<String>()
    val message: LiveData<String> = _message

    private val _downloadProgress = MutableLiveData<Map<String, Int>>()
    val downloadProgress: LiveData<Map<String, Int>> = _downloadProgress
    
    init {
        _isLoading.value = false
        loadDownloads()
        startPeriodicUpdates()

        // إعداد مستمع اكتمال التحميل
        downloadManager.setOnDownloadCompletedListener { download ->
            android.util.Log.d("DownloadViewModel", "اكتمل التحميل: ${download.title}")
            _message.value = "تم اكتمال تحميل: ${download.title}"
        }

        // إعداد مستمع تحديث التقدم
        downloadManager.setOnProgressUpdateListener { downloadId, progress ->
            val currentProgress = _downloadProgress.value?.toMutableMap() ?: mutableMapOf()
            currentProgress[downloadId] = progress
            _downloadProgress.value = currentProgress
        }
    }

    /**
     * بدء التحديثات الدورية
     */
    private fun startPeriodicUpdates() {
        viewModelScope.launch {
            while (true) {
                delay(500) // تحديث كل نصف ثانية لاستجابة أسرع
                loadDownloads()
            }
        }
    }
    
    /**
     * تحميل قائمة التحميلات
     */
    fun loadDownloads() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val downloadList = downloadManager.getAllDownloads()
                _downloads.value = downloadList
                _errorMessage.value = null
            } catch (e: Exception) {
                _errorMessage.value = e.message
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * بدء تحميل جديد
     */
    fun startDownload(url: String, quality: String, format: String) {
        viewModelScope.launch {
            try {
                android.util.Log.d("DownloadViewModel", "بدء تحميل جديد: $url")

                // عرض رسالة بدء التحميل
                _message.value = "جاري بدء التحميل..."

                val downloadId = UUID.randomUUID().toString()

                // استخراج المعلومات مع عرض رسالة
                _message.value = "جاري استخراج معلومات الملف..."
                val downloadInfo = extractInfoFromUrl(url)

                val download = Download(
                    id = downloadId,
                    url = url,
                    title = downloadInfo.title,
                    artist = downloadInfo.artist,
                    thumbnailUrl = downloadInfo.thumbnailUrl,
                    duration = downloadInfo.duration,
                    status = DownloadStatus.PENDING,
                    quality = quality,
                    format = format
                )

                android.util.Log.d("DownloadViewModel", "تم إنشاء التحميل: ${download.id} - ${download.title}")

                // عرض رسالة نجاح إضافة التحميل
                _message.value = "تم إضافة التحميل: ${download.title}"

                downloadManager.startDownload(download)

                android.util.Log.d("DownloadViewModel", "تم استدعاء downloadManager.startDownload")

                // تحديث فوري للقائمة
                loadDownloads()

            } catch (e: Exception) {
                android.util.Log.e("DownloadViewModel", "خطأ في بدء التحميل: ${e.message}")
                _errorMessage.value = e.message
                _message.value = "فشل في بدء التحميل: ${e.message}"
            }
        }
    }
    
    /**
     * إيقاف تحميل مؤقتاً
     */
    fun pauseDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.pauseDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * استئناف تحميل
     */
    fun resumeDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.resumeDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * إلغاء تحميل
     */
    fun cancelDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.cancelDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * إعادة محاولة تحميل
     */
    fun retryDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.retryDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * حذف تحميل
     */
    fun deleteDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.deleteDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * تشغيل ملف محمل
     */
    fun playDownloadedFile(download: Download) {
        viewModelScope.launch {
            try {
                downloadManager.playDownloadedFile(download)
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * معلومات الوسائط المستخرجة
     */
    data class MediaInfo(
        val title: String,
        val artist: String,
        val thumbnailUrl: String,
        val duration: Int
    )

    /**
     * استخراج معلومات شاملة من الرابط
     */
    private fun extractInfoFromUrl(url: String): MediaInfo {
        return try {
            when {
                url.contains("youtube.com") || url.contains("youtu.be") -> {
                    extractYouTubeInfo(url)
                }
                url.contains("soundcloud.com") -> {
                    extractSoundCloudInfo(url)
                }
                url.contains("spotify.com") -> {
                    extractSpotifyInfo(url)
                }
                url.contains("bandcamp.com") -> {
                    extractBandcampInfo(url)
                }
                url.contains("mixcloud.com") -> {
                    extractMixcloudInfo(url)
                }
                else -> {
                    extractGenericInfo(url)
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("DownloadViewModel", "خطأ في استخراج المعلومات: ${e.message}")
            MediaInfo(
                title = "أغنية غير معروفة",
                artist = "فنان غير معروف",
                thumbnailUrl = "",
                duration = 180
            )
        }
    }

    /**
     * استخراج معلومات YouTube
     */
    private fun extractYouTubeInfo(url: String): MediaInfo {
        val videoId = extractYouTubeVideoId(url)
        return MediaInfo(
            title = "فيديو YouTube - جاري استخراج المعلومات...",
            artist = "قناة YouTube",
            thumbnailUrl = if (videoId.isNotEmpty()) {
                "https://img.youtube.com/vi/$videoId/maxresdefault.jpg"
            } else {
                "https://via.placeholder.com/300x300/ff0000/ffffff?text=YouTube"
            },
            duration = 180
        )
    }

    /**
     * استخراج معلومات SoundCloud
     */
    private fun extractSoundCloudInfo(url: String): MediaInfo {
        val trackName = url.substringAfterLast("/").replace("-", " ").replace("_", " ")
        return MediaInfo(
            title = if (trackName.isNotEmpty()) "SoundCloud: $trackName" else "مقطع SoundCloud",
            artist = "فنان SoundCloud",
            thumbnailUrl = "https://via.placeholder.com/300x300/ff6600/ffffff?text=SoundCloud",
            duration = 240
        )
    }

    /**
     * استخراج معلومات Spotify
     */
    private fun extractSpotifyInfo(url: String): MediaInfo {
        return MediaInfo(
            title = "مقطع Spotify - جاري استخراج المعلومات...",
            artist = "فنان Spotify",
            thumbnailUrl = "https://via.placeholder.com/300x300/1db954/ffffff?text=Spotify",
            duration = 200
        )
    }

    /**
     * استخراج معلومات Bandcamp
     */
    private fun extractBandcampInfo(url: String): MediaInfo {
        val trackName = url.substringAfterLast("/").replace("-", " ").replace("_", " ")
        return MediaInfo(
            title = if (trackName.isNotEmpty()) "Bandcamp: $trackName" else "مقطع Bandcamp",
            artist = "فنان Bandcamp",
            thumbnailUrl = "https://via.placeholder.com/300x300/629aa0/ffffff?text=Bandcamp",
            duration = 220
        )
    }

    /**
     * استخراج معلومات Mixcloud
     */
    private fun extractMixcloudInfo(url: String): MediaInfo {
        val mixName = url.substringAfterLast("/").replace("-", " ").replace("_", " ")
        return MediaInfo(
            title = if (mixName.isNotEmpty()) "Mixcloud: $mixName" else "مزيج Mixcloud",
            artist = "DJ Mixcloud",
            thumbnailUrl = "https://via.placeholder.com/300x300/314359/ffffff?text=Mixcloud",
            duration = 3600 // ساعة واحدة للمزيج
        )
    }

    /**
     * استخراج معلومات عامة
     */
    private fun extractGenericInfo(url: String): MediaInfo {
        val fileName = url.substringAfterLast("/").substringBeforeLast(".")
        val cleanFileName = fileName.replace("-", " ").replace("_", " ").replace("%20", " ")

        return MediaInfo(
            title = if (cleanFileName.isNotEmpty()) cleanFileName else "ملف صوتي",
            artist = "فنان غير معروف",
            thumbnailUrl = "https://via.placeholder.com/300x300/333333/ffffff?text=Music",
            duration = 210
        )
    }

    /**
     * استخراج معرف فيديو YouTube
     */
    private fun extractYouTubeVideoId(url: String): String {
        return try {
            when {
                url.contains("youtu.be/") -> {
                    url.substringAfter("youtu.be/").substringBefore("?").substringBefore("&")
                }
                url.contains("youtube.com/watch?v=") -> {
                    url.substringAfter("v=").substringBefore("&")
                }
                url.contains("youtube.com/embed/") -> {
                    url.substringAfter("embed/").substringBefore("?")
                }
                else -> ""
            }
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * مسح رسالة الخطأ
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * مسح جميع التحميلات المكتملة
     */
    fun clearCompletedDownloads() {
        viewModelScope.launch {
            try {
                downloadManager.clearCompletedDownloads()
                loadDownloads()
                _message.value = "تم مسح التحميلات المكتملة"
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }

    /**
     * إيقاف جميع التحميلات
     */
    fun pauseAllDownloads() {
        viewModelScope.launch {
            try {
                downloadManager.pauseAllDownloads()
                loadDownloads()
                _message.value = "تم إيقاف جميع التحميلات"
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }

    /**
     * استئناف جميع التحميلات
     */
    fun resumeAllDownloads() {
        viewModelScope.launch {
            try {
                downloadManager.resumeAllDownloads()
                loadDownloads()
                _message.value = "تم استئناف جميع التحميلات"
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
}
