package com.musicplayer.pro.viewmodels

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.models.DownloadStatus
import com.musicplayer.pro.managers.DownloadManager
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.util.*

/**
 * ViewModel للتحميلات - مطابق لـ DownloadManager في Python
 */
class DownloadViewModel(application: Application) : AndroidViewModel(application) {
    
    private val downloadManager = DownloadManager(application)
    
    // البيانات المباشرة
    private val _downloads = MutableLiveData<List<Download>>()
    val downloads: LiveData<List<Download>> = _downloads
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    init {
        _isLoading.value = false
        loadDownloads()
        startPeriodicUpdates()

        // إعداد مستمع اكتمال التحميل
        downloadManager.setOnDownloadCompletedListener { download ->
            android.util.Log.d("DownloadViewModel", "اكتمل التحميل: ${download.title}")
            // يمكن إضافة منطق إضافي هنا مثل إشعار المستخدم
        }
    }

    /**
     * بدء التحديثات الدورية
     */
    private fun startPeriodicUpdates() {
        viewModelScope.launch {
            while (true) {
                delay(500) // تحديث كل نصف ثانية لاستجابة أسرع
                loadDownloads()
            }
        }
    }
    
    /**
     * تحميل قائمة التحميلات
     */
    fun loadDownloads() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val downloadList = downloadManager.getAllDownloads()
                _downloads.value = downloadList
                _errorMessage.value = null
            } catch (e: Exception) {
                _errorMessage.value = e.message
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * بدء تحميل جديد
     */
    fun startDownload(url: String, quality: String, format: String) {
        viewModelScope.launch {
            try {
                android.util.Log.d("DownloadViewModel", "بدء تحميل جديد: $url")

                val downloadId = UUID.randomUUID().toString()
                val downloadInfo = extractInfoFromUrl(url)
                val download = Download(
                    id = downloadId,
                    url = url,
                    title = downloadInfo.title,
                    artist = downloadInfo.artist,
                    thumbnailUrl = downloadInfo.thumbnailUrl,
                    duration = downloadInfo.duration,
                    status = DownloadStatus.PENDING,
                    quality = quality,
                    format = format
                )

                android.util.Log.d("DownloadViewModel", "تم إنشاء التحميل: ${download.id}")

                downloadManager.startDownload(download)

                android.util.Log.d("DownloadViewModel", "تم استدعاء downloadManager.startDownload")

                // تحديث فوري للقائمة
                loadDownloads()

            } catch (e: Exception) {
                android.util.Log.e("DownloadViewModel", "خطأ في بدء التحميل: ${e.message}")
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * إيقاف تحميل مؤقتاً
     */
    fun pauseDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.pauseDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * استئناف تحميل
     */
    fun resumeDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.resumeDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * إلغاء تحميل
     */
    fun cancelDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.cancelDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * إعادة محاولة تحميل
     */
    fun retryDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.retryDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * حذف تحميل
     */
    fun deleteDownload(downloadId: String) {
        viewModelScope.launch {
            try {
                downloadManager.deleteDownload(downloadId)
                loadDownloads()
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * تشغيل ملف محمل
     */
    fun playDownloadedFile(download: Download) {
        viewModelScope.launch {
            try {
                downloadManager.playDownloadedFile(download)
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * معلومات الوسائط المستخرجة
     */
    data class MediaInfo(
        val title: String,
        val artist: String,
        val thumbnailUrl: String,
        val duration: Int
    )

    /**
     * استخراج معلومات شاملة من الرابط
     */
    private fun extractInfoFromUrl(url: String): MediaInfo {
        return try {
            when {
                url.contains("youtube.com") || url.contains("youtu.be") -> {
                    MediaInfo(
                        title = "أغنية من YouTube",
                        artist = "فنان YouTube",
                        thumbnailUrl = "https://img.youtube.com/vi/default/maxresdefault.jpg",
                        duration = 180 // 3 دقائق كمثال
                    )
                }
                url.contains("soundcloud.com") -> {
                    MediaInfo(
                        title = "أغنية من SoundCloud",
                        artist = "فنان SoundCloud",
                        thumbnailUrl = "https://via.placeholder.com/300x300/ff6600/ffffff?text=SoundCloud",
                        duration = 240 // 4 دقائق كمثال
                    )
                }
                url.contains("spotify.com") -> {
                    MediaInfo(
                        title = "أغنية من Spotify",
                        artist = "فنان Spotify",
                        thumbnailUrl = "https://via.placeholder.com/300x300/1db954/ffffff?text=Spotify",
                        duration = 200 // 3:20 دقائق كمثال
                    )
                }
                else -> {
                    // استخراج اسم الملف من الرابط
                    val fileName = url.substringAfterLast("/").substringBeforeLast(".")
                    MediaInfo(
                        title = fileName.ifEmpty { "أغنية محملة" },
                        artist = "فنان غير معروف",
                        thumbnailUrl = "https://via.placeholder.com/300x300/333333/ffffff?text=Music",
                        duration = 210 // 3:30 دقائق كمثال
                    )
                }
            }
        } catch (e: Exception) {
            MediaInfo(
                title = "أغنية غير معروفة",
                artist = "فنان غير معروف",
                thumbnailUrl = "",
                duration = 180
            )
        }
    }
    
    /**
     * مسح رسالة الخطأ
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
}
