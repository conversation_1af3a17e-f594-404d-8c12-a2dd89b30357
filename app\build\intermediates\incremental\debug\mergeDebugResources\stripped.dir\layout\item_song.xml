<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:background="?attr/selectableItemBackground"
    android:padding="12dp">

    <!-- صورة الغلاف -->
    <androidx.cardview.widget.CardView
        android:id="@+id/albumCoverCard"
        android:layout_width="48dp"
        android:layout_height="48dp"
        app:cardCornerRadius="6dp"
        app:cardElevation="2dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/albumCover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/default_album_cover" />

    </androidx.cardview.widget.CardView>

    <!-- معلومات الأغنية -->
    <LinearLayout
        android:id="@+id/songInfoLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/duration"
        app:layout_constraintStart_toEndOf="@+id/albumCoverCard"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/songTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="Song Title" />

        <TextView
            android:id="@+id/artistName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            tools:text="Artist Name" />

    </LinearLayout>

    <!-- المدة -->
    <TextView
        android:id="@+id/duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/favoriteIcon"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="3:45" />

    <!-- أيقونة المفضلة -->
    <ImageView
        android:id="@+id/favoriteIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="@string/favorite"
        android:src="@drawable/ic_favorite_outline"
        android:tint="@color/text_secondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
