// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentThemeSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialCardView currentThemeCard;

  @NonNull
  public final View currentThemeColor;

  @NonNull
  public final TextView currentThemeDescription;

  @NonNull
  public final TextView currentThemeName;

  @NonNull
  public final SwitchMaterial dynamicColorsSwitch;

  @NonNull
  public final SwitchMaterial followSystemSwitch;

  @NonNull
  public final RecyclerView themesRecyclerView;

  private FragmentThemeSettingsBinding(@NonNull ScrollView rootView,
      @NonNull MaterialCardView currentThemeCard, @NonNull View currentThemeColor,
      @NonNull TextView currentThemeDescription, @NonNull TextView currentThemeName,
      @NonNull SwitchMaterial dynamicColorsSwitch, @NonNull SwitchMaterial followSystemSwitch,
      @NonNull RecyclerView themesRecyclerView) {
    this.rootView = rootView;
    this.currentThemeCard = currentThemeCard;
    this.currentThemeColor = currentThemeColor;
    this.currentThemeDescription = currentThemeDescription;
    this.currentThemeName = currentThemeName;
    this.dynamicColorsSwitch = dynamicColorsSwitch;
    this.followSystemSwitch = followSystemSwitch;
    this.themesRecyclerView = themesRecyclerView;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentThemeSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentThemeSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_theme_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentThemeSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.currentThemeCard;
      MaterialCardView currentThemeCard = ViewBindings.findChildViewById(rootView, id);
      if (currentThemeCard == null) {
        break missingId;
      }

      id = R.id.currentThemeColor;
      View currentThemeColor = ViewBindings.findChildViewById(rootView, id);
      if (currentThemeColor == null) {
        break missingId;
      }

      id = R.id.currentThemeDescription;
      TextView currentThemeDescription = ViewBindings.findChildViewById(rootView, id);
      if (currentThemeDescription == null) {
        break missingId;
      }

      id = R.id.currentThemeName;
      TextView currentThemeName = ViewBindings.findChildViewById(rootView, id);
      if (currentThemeName == null) {
        break missingId;
      }

      id = R.id.dynamicColorsSwitch;
      SwitchMaterial dynamicColorsSwitch = ViewBindings.findChildViewById(rootView, id);
      if (dynamicColorsSwitch == null) {
        break missingId;
      }

      id = R.id.followSystemSwitch;
      SwitchMaterial followSystemSwitch = ViewBindings.findChildViewById(rootView, id);
      if (followSystemSwitch == null) {
        break missingId;
      }

      id = R.id.themesRecyclerView;
      RecyclerView themesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (themesRecyclerView == null) {
        break missingId;
      }

      return new FragmentThemeSettingsBinding((ScrollView) rootView, currentThemeCard,
          currentThemeColor, currentThemeDescription, currentThemeName, dynamicColorsSwitch,
          followSystemSwitch, themesRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
