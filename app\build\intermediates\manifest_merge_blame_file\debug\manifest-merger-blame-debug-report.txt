1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.musicplayer.pro"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- الأذونات الأساسية -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:6:5-67
12-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:7:5-79
13-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:8:5-76
14-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- أذونات التخزين -->
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:11:5-80
17-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:11:22-77
18    <uses-permission
18-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="28" />
20-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
21-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:14:5-15:40
21-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:14:22-79
22    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
22-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:16:5-75
22-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:16:22-72
23
24    <!-- أذونات الصوت -->
25    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
25-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:19:5-80
25-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:19:22-77
26    <uses-permission android:name="android.permission.RECORD_AUDIO" />
26-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:20:5-71
26-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:20:22-68
27
28    <!-- أذونات الخدمات -->
29    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
29-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:23:5-77
29-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:23:22-74
30    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
30-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:24:5-92
30-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:24:22-89
31    <uses-permission android:name="android.permission.WAKE_LOCK" />
31-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:25:5-68
31-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:25:22-65
32    <uses-permission android:name="android.permission.VIBRATE" />
32-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:26:5-66
32-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:26:22-63
33
34    <!-- أذونات البلوتوث -->
35    <uses-permission android:name="android.permission.BLUETOOTH" />
35-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:29:5-68
35-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:29:22-65
36    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
36-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:30:5-74
36-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:30:22-71
37    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
37-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:31:5-76
37-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:31:22-73
38    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
38-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:32:5-73
38-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:32:22-70
39
40    <!-- أذونات الإشعارات -->
41    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
41-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:35:5-77
41-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:35:22-74
42    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
42-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:36:5-85
42-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:36:22-82
43    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
43-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:37:5-93
43-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:37:22-90
44
45    <!-- أذونات إضافية (اختيارية) -->
46    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
46-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:40:5-81
46-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:40:22-78
47    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
47-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:41:5-75
47-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:41:22-72
48    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />
48-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:42:5-80
48-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:42:22-77
49
50    <!-- دعم الميزات -->
51    <uses-feature
51-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:45:5-47:35
52        android:name="android.hardware.audio.output"
52-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:46:9-53
53        android:required="true" />
53-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:47:9-32
54    <uses-feature
54-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:48:5-50:36
55        android:name="android.hardware.microphone"
55-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:49:9-51
56        android:required="false" />
56-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:50:9-33
57    <uses-feature
57-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:51:5-53:36
58        android:name="android.hardware.bluetooth"
58-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:52:9-50
59        android:required="false" />
59-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:53:9-33
60    <uses-feature
60-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:54:5-56:36
61        android:name="android.hardware.wifi"
61-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:55:9-45
62        android:required="false" />
62-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:56:9-33
63
64    <!-- دعم Android Auto -->
65    <uses-feature
65-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:214:5-216:36
66        android:name="android.hardware.type.automotive"
66-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:215:9-56
67        android:required="false" />
67-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:216:9-33
68
69    <!-- دعم Android TV -->
70    <uses-feature
70-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:219:5-221:36
71        android:name="android.software.leanback"
71-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:220:9-49
72        android:required="false" />
72-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:221:9-33
73
74    <!-- دعم Wear OS -->
75    <uses-feature
75-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:224:5-226:36
76        android:name="android.hardware.type.watch"
76-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:225:9-51
77        android:required="false" />
77-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:226:9-33
78
79    <permission
79-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
80        android:name="com.musicplayer.pro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
80-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
81        android:protectionLevel="signature" />
81-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
82
83    <uses-permission android:name="com.musicplayer.pro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
83-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
83-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
84
85    <application
85-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:58:5-211:19
86        android:name="com.musicplayer.pro.MusicPlayerApplication"
86-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:59:9-47
87        android:allowBackup="true"
87-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:60:9-35
88        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
88-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c108dd56627f30fe94755d1a2faeaf2\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
89        android:dataExtractionRules="@xml/data_extraction_rules"
89-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:61:9-65
90        android:debuggable="true"
91        android:extractNativeLibs="true"
92        android:fullBackupContent="@xml/backup_rules"
92-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:62:9-54
93        android:icon="@mipmap/ic_launcher"
93-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:63:9-43
94        android:label="@string/app_name"
94-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:64:9-41
95        android:preserveLegacyExternalStorage="true"
95-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:69:9-53
96        android:requestLegacyExternalStorage="true"
96-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:68:9-52
97        android:roundIcon="@mipmap/ic_launcher_round"
97-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:65:9-54
98        android:supportsRtl="true"
98-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:66:9-35
99        android:testOnly="true"
100        android:theme="@style/Theme.MusicPlayerPro" >
100-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:67:9-52
101
102        <!-- النشاط الرئيسي -->
103        <activity
103-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:73:9-104:20
104            android:name="com.musicplayer.pro.MainActivity"
104-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:74:13-41
105            android:exported="true"
105-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:75:13-36
106            android:launchMode="singleTop"
106-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:76:13-43
107            android:screenOrientation="portrait"
107-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:77:13-49
108            android:theme="@style/Theme.MusicPlayerPro" >
108-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:78:13-56
109            <intent-filter>
109-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:79:13-82:29
110                <action android:name="android.intent.action.MAIN" />
110-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:80:17-69
110-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:80:25-66
111
112                <category android:name="android.intent.category.LAUNCHER" />
112-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:81:17-77
112-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:81:27-74
113            </intent-filter>
114
115            <!-- دعم ملفات الصوت -->
116            <intent-filter>
116-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:85:13-90:29
117                <action android:name="android.intent.action.VIEW" />
117-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:86:17-69
117-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:86:25-66
118
119                <category android:name="android.intent.category.DEFAULT" />
119-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:87:17-76
119-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:87:27-73
120                <category android:name="android.intent.category.BROWSABLE" />
120-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:88:17-78
120-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:88:27-75
121
122                <data android:mimeType="audio/*" />
122-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
122-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:23-49
123            </intent-filter>
124
125            <!-- دعم الروابط -->
126            <intent-filter android:autoVerify="true" >
126-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:93:13-103:29
126-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:93:28-53
127                <action android:name="android.intent.action.VIEW" />
127-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:86:17-69
127-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:86:25-66
128
129                <category android:name="android.intent.category.DEFAULT" />
129-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:87:17-76
129-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:87:27-73
130                <category android:name="android.intent.category.BROWSABLE" />
130-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:88:17-78
130-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:88:27-75
131
132                <data
132-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
133                    android:host="youtube.com"
133-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
134                    android:scheme="http" />
134-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
135                <data
135-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
136                    android:host="youtube.com"
136-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
137                    android:scheme="https" />
137-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
138                <data
138-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
139                    android:host="www.youtube.com"
139-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
140                    android:scheme="http" />
140-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
141                <data
141-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
142                    android:host="www.youtube.com"
142-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
143                    android:scheme="https" />
143-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
144                <data
144-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
145                    android:host="youtu.be"
145-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
146                    android:scheme="http" />
146-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
147                <data
147-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
148                    android:host="youtu.be"
148-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:45-71
149                    android:scheme="https" />
149-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
150            </intent-filter>
151        </activity>
152
153        <!-- نشاط الإعدادات -->
154        <activity
154-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:107:9-115:20
155            android:name="com.musicplayer.pro.SettingsActivity"
155-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:108:13-45
156            android:exported="false"
156-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:109:13-37
157            android:parentActivityName="com.musicplayer.pro.MainActivity"
157-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:111:13-55
158            android:theme="@style/Theme.MusicPlayerPro" >
158-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:110:13-56
159            <meta-data
159-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:112:13-114:49
160                android:name="android.support.PARENT_ACTIVITY"
160-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:113:17-63
161                android:value=".MainActivity" />
161-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:114:17-46
162        </activity>
163
164        <!-- نشاط المعادل الصوتي -->
165        <activity
165-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:118:9-126:20
166            android:name="com.musicplayer.pro.EqualizerActivity"
166-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:119:13-46
167            android:exported="false"
167-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:120:13-37
168            android:parentActivityName="com.musicplayer.pro.SettingsActivity"
168-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:122:13-59
169            android:theme="@style/Theme.MusicPlayerPro" >
169-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:121:13-56
170            <meta-data
170-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:112:13-114:49
171                android:name="android.support.PARENT_ACTIVITY"
171-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:113:17-63
172                android:value=".SettingsActivity" />
172-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:114:17-46
173        </activity>
174
175        <!-- خدمة الموسيقى -->
176        <service
176-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:129:9-137:19
177            android:name="com.musicplayer.pro.services.MusicService"
177-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:130:13-50
178            android:enabled="true"
178-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:131:13-35
179            android:exported="false"
179-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:132:13-37
180            android:foregroundServiceType="mediaPlayback" >
180-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:133:13-58
181            <intent-filter>
181-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:134:13-136:29
182                <action android:name="android.media.browse.MediaBrowserService" />
182-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:135:17-83
182-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:135:25-80
183            </intent-filter>
184        </service>
185
186        <!-- خدمة التحميل -->
187        <service
187-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:140:9-144:56
188            android:name="com.musicplayer.pro.services.DownloadService"
188-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:141:13-53
189            android:enabled="true"
189-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:142:13-35
190            android:exported="false"
190-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:143:13-37
191            android:foregroundServiceType="dataSync" />
191-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:144:13-53
192
193        <!-- مستقبل البث للتحكم في الوسائط -->
194        <receiver
194-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:147:9-154:20
195            android:name="com.musicplayer.pro.receivers.MediaButtonReceiver"
195-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:148:13-58
196            android:enabled="true"
196-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:149:13-35
197            android:exported="true" >
197-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:150:13-36
198            <intent-filter android:priority="1000" >
198-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:151:13-153:29
198-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:151:28-51
199                <action android:name="android.intent.action.MEDIA_BUTTON" />
199-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:152:17-77
199-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:152:25-74
200            </intent-filter>
201        </receiver>
202
203        <!-- مستقبل البث لبدء التشغيل -->
204        <receiver
204-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:157:9-167:20
205            android:name="com.musicplayer.pro.receivers.BootReceiver"
205-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:158:13-51
206            android:enabled="true"
206-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:159:13-35
207            android:exported="true" >
207-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:160:13-36
208            <intent-filter android:priority="1000" >
208-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:161:13-166:29
208-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:161:28-51
209                <action android:name="android.intent.action.BOOT_COMPLETED" />
209-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:162:17-79
209-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:162:25-76
210                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
210-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:163:17-84
210-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:163:25-81
211                <action android:name="android.intent.action.PACKAGE_REPLACED" />
211-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:164:17-81
211-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:164:25-78
212
213                <data android:scheme="package" />
213-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:89:17-52
213-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:97:23-44
214            </intent-filter>
215        </receiver>
216
217        <!-- مستقبل البث للسماعات -->
218        <receiver
218-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:170:9-178:20
219            android:name="com.musicplayer.pro.receivers.HeadsetReceiver"
219-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:171:13-54
220            android:enabled="true"
220-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:172:13-35
221            android:exported="true" >
221-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:173:13-36
222            <intent-filter>
222-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:174:13-177:29
223                <action android:name="android.intent.action.HEADSET_PLUG" />
223-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:175:17-77
223-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:175:25-74
224                <action android:name="android.bluetooth.a2dp.profile.action.CONNECTION_STATE_CHANGED" />
224-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:176:17-105
224-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:176:25-102
225            </intent-filter>
226        </receiver>
227
228        <!-- مزود المحتوى للملفات -->
229        <provider
230            android:name="androidx.core.content.FileProvider"
230-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:182:13-62
231            android:authorities="com.musicplayer.pro.fileprovider"
231-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:183:13-64
232            android:exported="false"
232-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:184:13-37
233            android:grantUriPermissions="true" >
233-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:185:13-47
234            <meta-data
234-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:186:13-188:54
235                android:name="android.support.FILE_PROVIDER_PATHS"
235-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:187:17-67
236                android:resource="@xml/file_paths" />
236-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:188:17-51
237        </provider>
238
239        <!-- MediaSession -->
240        <meta-data
240-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:192:9-194:36
241            android:name="android.media.session.MediaSession"
241-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:193:13-62
242            android:value="true" />
242-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:194:13-33
243
244        <!-- دعم Auto -->
245        <meta-data
245-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:197:9-199:59
246            android:name="com.google.android.gms.car.application"
246-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:198:13-66
247            android:resource="@xml/automotive_app_desc" />
247-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:199:13-56
248
249        <!-- دعم Wear OS -->
250        <meta-data
250-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:202:9-204:37
251            android:name="com.google.android.wearable.standalone"
251-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:203:13-66
252            android:value="false" />
252-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:204:13-34
253
254        <!-- إعدادات الشبكة -->
255        <meta-data
255-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:207:9-209:36
256            android:name="android.webkit.WebView.MetricsOptOut"
256-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:208:13-64
257            android:value="true" />
257-->E:\python\PythonProject1\kotlin_version\app\src\main\AndroidManifest.xml:209:13-33
258
259        <activity
259-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
260            android:name="com.karumi.dexter.DexterActivity"
260-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
261            android:theme="@style/Dexter.Internal.Theme.Transparent" />
261-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b7043eedc50cada365136aa0e9ac9a0\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
262
263        <provider
263-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
264            android:name="androidx.startup.InitializationProvider"
264-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
265            android:authorities="com.musicplayer.pro.androidx-startup"
265-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
266            android:exported="false" >
266-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
267            <meta-data
267-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
268                android:name="androidx.emoji2.text.EmojiCompatInitializer"
268-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
269                android:value="androidx.startup" />
269-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7eec3ffd9134b39a33221bcb756c708\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
270            <meta-data
270-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
271                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
271-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
272                android:value="androidx.startup" />
272-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47b54bf287343bbce7eafca9f7c47b52\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
273            <meta-data
273-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
274                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
274-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
275                android:value="androidx.startup" />
275-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
276        </provider>
277
278        <uses-library
278-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
279            android:name="androidx.window.extensions"
279-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
280            android:required="false" />
280-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
281        <uses-library
281-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
282            android:name="androidx.window.sidecar"
282-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
283            android:required="false" />
283-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b7dbe88d3a04c6d55aa7c581f20597\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
284
285        <receiver
285-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
286            android:name="androidx.profileinstaller.ProfileInstallReceiver"
286-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
287            android:directBootAware="false"
287-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
288            android:enabled="true"
288-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
289            android:exported="true"
289-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
290            android:permission="android.permission.DUMP" >
290-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
291            <intent-filter>
291-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
292                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
292-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
292-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
293            </intent-filter>
294            <intent-filter>
294-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
295                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
295-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
295-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
296            </intent-filter>
297            <intent-filter>
297-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
298                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
298-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
298-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
299            </intent-filter>
300            <intent-filter>
300-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
301                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
301-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
301-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40d0c9c25ac4e4faa2b18010b8a7d617\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
302            </intent-filter>
303        </receiver>
304    </application>
305
306</manifest>
