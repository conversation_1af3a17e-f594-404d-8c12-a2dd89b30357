[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_reset.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_reset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_bass.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_bass.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\mipmap-hdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\mipmap-hdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_skip_next.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_skip_next.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_clear.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_clear.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_quality_badge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\quality_badge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_audio_effects.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_audio_effects.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_warning.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_warning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_restore.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_restore.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_download_quality.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_download_quality.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_refresh.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_refresh.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_storage.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_storage.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_item_download.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\item_download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_item_song.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\item_song.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_item_equalizer_band.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\item_equalizer_band.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_music_library.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_music_library.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_playlist_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_playlist_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_repeat_one.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_repeat_one.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_favorite_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_favorite_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_fragment_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\fragment_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_bottom_bar_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\bottom_bar_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_now_playing.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_now_playing.xml"}, {"merged": "com.musicplayer.pro.app-merged_res-57:/layout_item_download.xml.flat", "source": "com.musicplayer.pro.app-main-59:/layout/item_download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_bottom_music_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\bottom_music_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_shuffle_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_shuffle_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_share.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_share.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_skip_previous.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_skip_previous.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_play_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\play_button_background.xml"}, {"merged": "com.musicplayer.pro.app-merged_res-57:/drawable_bg_quality_badge.xml.flat", "source": "com.musicplayer.pro.app-main-59:/drawable/bg_quality_badge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_pause.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_pause.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_repeat_off.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_repeat_off.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_folder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_folder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_loading.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_loading.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_file_format.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_file_format.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_link.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_link.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_activity_equalizer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\activity_equalizer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_equalizer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_equalizer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_backup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_backup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\menu_main_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\menu\\main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_download.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_theme.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_theme.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_fragment_now_playing.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\fragment_now_playing.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_exit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_exit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_palette.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_palette.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_fragment_download.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\fragment_download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_wifi.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_wifi.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_star.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_star.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\xml_preferences.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\xml\\preferences.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_high_quality.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_high_quality.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_license.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_license.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_audio.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_audio.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_default_album_cover.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\default_album_cover.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_repeat_all.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_repeat_all.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_spinner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\spinner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_playing_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\playing_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\xml_automotive_app_desc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\xml\\automotive_app_desc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_shuffle_on.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_shuffle_on.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_language.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_language.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_music_note.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_music_note.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_play.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_play.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_favorite_filled.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_favorite_filled.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_surround_sound.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_surround_sound.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_dialog_add_download.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\dialog_add_download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_ic_update.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\ic_update.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\drawable_control_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\drawable\\control_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-merged_res-57:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.musicplayer.pro.app-main-59:\\layout\\activity_settings.xml"}]