<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_theme_settings" modulePackage="com.musicplayer.pro" filePath="app\src\main\res\layout\fragment_theme_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_theme_settings_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="206" endOffset="12"/></Target><Target id="@+id/currentThemeCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="25" startOffset="8" endLine="84" endOffset="59"/></Target><Target id="@+id/currentThemeColor" view="View"><Expressions/><location startLine="41" startOffset="16" endLine="46" endOffset="73"/></Target><Target id="@+id/currentThemeName" view="TextView"><Expressions/><location startLine="63" startOffset="20" endLine="70" endOffset="59"/></Target><Target id="@+id/currentThemeDescription" view="TextView"><Expressions/><location startLine="72" startOffset="20" endLine="78" endOffset="73"/></Target><Target id="@+id/dynamicColorsSwitch" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="139" startOffset="20" endLine="142" endOffset="62"/></Target><Target id="@+id/followSystemSwitch" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="175" startOffset="20" endLine="178" endOffset="62"/></Target><Target id="@+id/themesRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="196" startOffset="8" endLine="202" endOffset="49"/></Target></Targets></Layout>