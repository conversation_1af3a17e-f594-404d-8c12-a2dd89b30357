package com.musicplayer.pro.database

import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import com.musicplayer.pro.models.Song
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * قاعدة بيانات بسيطة للأغاني المحملة
 */
class SimpleMusicDatabase(context: Context) : SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {
    
    companion object {
        private const val DATABASE_NAME = "music_database.db"
        private const val DATABASE_VERSION = 1
        
        // جدول الأغاني
        private const val TABLE_SONGS = "songs"
        private const val COLUMN_ID = "id"
        private const val COLUMN_TITLE = "title"
        private const val COLUMN_ARTIST = "artist"
        private const val COLUMN_ALBUM = "album"
        private const val COLUMN_DURATION = "duration"
        private const val COLUMN_PATH = "path"
        private const val COLUMN_SIZE = "size"
        private const val COLUMN_MIME_TYPE = "mime_type"
        private const val COLUMN_DATE_ADDED = "date_added"
        private const val COLUMN_IS_FROM_DOWNLOAD = "is_from_download"
        private const val COLUMN_DOWNLOAD_URL = "download_url"
        private const val COLUMN_IS_FAVORITE = "is_favorite"
        private const val COLUMN_PLAY_COUNT = "play_count"
        private const val COLUMN_LAST_PLAYED = "last_played"
        private const val COLUMN_RATING = "rating"
        
        @Volatile
        private var INSTANCE: SimpleMusicDatabase? = null
        
        fun getInstance(context: Context): SimpleMusicDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = SimpleMusicDatabase(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }
    
    override fun onCreate(db: SQLiteDatabase) {
        val createSongsTable = """
            CREATE TABLE $TABLE_SONGS (
                $COLUMN_ID INTEGER PRIMARY KEY AUTOINCREMENT,
                $COLUMN_TITLE TEXT NOT NULL,
                $COLUMN_ARTIST TEXT NOT NULL,
                $COLUMN_ALBUM TEXT NOT NULL,
                $COLUMN_DURATION INTEGER DEFAULT 0,
                $COLUMN_PATH TEXT NOT NULL UNIQUE,
                $COLUMN_SIZE INTEGER DEFAULT 0,
                $COLUMN_MIME_TYPE TEXT DEFAULT '',
                $COLUMN_DATE_ADDED INTEGER DEFAULT 0,
                $COLUMN_IS_FROM_DOWNLOAD INTEGER DEFAULT 0,
                $COLUMN_DOWNLOAD_URL TEXT DEFAULT '',
                $COLUMN_IS_FAVORITE INTEGER DEFAULT 0,
                $COLUMN_PLAY_COUNT INTEGER DEFAULT 0,
                $COLUMN_LAST_PLAYED INTEGER DEFAULT 0,
                $COLUMN_RATING REAL DEFAULT 0.0
            )
        """.trimIndent()
        
        db.execSQL(createSongsTable)
        
        // إنشاء فهارس للبحث السريع
        db.execSQL("CREATE INDEX idx_title ON $TABLE_SONGS($COLUMN_TITLE)")
        db.execSQL("CREATE INDEX idx_artist ON $TABLE_SONGS($COLUMN_ARTIST)")
        db.execSQL("CREATE INDEX idx_album ON $TABLE_SONGS($COLUMN_ALBUM)")
        db.execSQL("CREATE INDEX idx_is_from_download ON $TABLE_SONGS($COLUMN_IS_FROM_DOWNLOAD)")
    }
    
    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        db.execSQL("DROP TABLE IF EXISTS $TABLE_SONGS")
        onCreate(db)
    }
    
    /**
     * إضافة أغنية جديدة
     */
    suspend fun insertSong(song: Song): Long = withContext(Dispatchers.IO) {
        val db = writableDatabase
        val values = ContentValues().apply {
            put(COLUMN_TITLE, song.title)
            put(COLUMN_ARTIST, song.artist)
            put(COLUMN_ALBUM, song.album)
            put(COLUMN_DURATION, song.duration)
            put(COLUMN_PATH, song.path)
            put(COLUMN_SIZE, song.size)
            put(COLUMN_MIME_TYPE, song.mimeType)
            put(COLUMN_DATE_ADDED, song.dateAdded)
            put(COLUMN_IS_FROM_DOWNLOAD, if (song.isFromDownload) 1 else 0)
            put(COLUMN_DOWNLOAD_URL, song.downloadUrl)
            put(COLUMN_IS_FAVORITE, if (song.isFavorite) 1 else 0)
            put(COLUMN_PLAY_COUNT, song.playCount)
            put(COLUMN_LAST_PLAYED, song.lastPlayed)
            put(COLUMN_RATING, song.rating)
        }
        
        try {
            db.insertWithOnConflict(TABLE_SONGS, null, values, SQLiteDatabase.CONFLICT_REPLACE)
        } catch (e: Exception) {
            android.util.Log.e("SimpleMusicDatabase", "خطأ في إضافة الأغنية: ${e.message}")
            -1L
        }
    }
    
    /**
     * الحصول على جميع الأغاني المحملة
     */
    suspend fun getDownloadedSongs(): List<Song> = withContext(Dispatchers.IO) {
        val songs = mutableListOf<Song>()
        val db = readableDatabase
        
        try {
            val cursor = db.query(
                TABLE_SONGS,
                null,
                "$COLUMN_IS_FROM_DOWNLOAD = ?",
                arrayOf("1"),
                null,
                null,
                "$COLUMN_DATE_ADDED DESC"
            )
            
            cursor.use {
                while (it.moveToNext()) {
                    val song = Song(
                        id = it.getLong(it.getColumnIndexOrThrow(COLUMN_ID)),
                        title = it.getString(it.getColumnIndexOrThrow(COLUMN_TITLE)),
                        artist = it.getString(it.getColumnIndexOrThrow(COLUMN_ARTIST)),
                        album = it.getString(it.getColumnIndexOrThrow(COLUMN_ALBUM)),
                        duration = it.getLong(it.getColumnIndexOrThrow(COLUMN_DURATION)),
                        path = it.getString(it.getColumnIndexOrThrow(COLUMN_PATH)),
                        size = it.getLong(it.getColumnIndexOrThrow(COLUMN_SIZE)),
                        mimeType = it.getString(it.getColumnIndexOrThrow(COLUMN_MIME_TYPE)),
                        dateAdded = it.getLong(it.getColumnIndexOrThrow(COLUMN_DATE_ADDED)),
                        isFromDownload = it.getInt(it.getColumnIndexOrThrow(COLUMN_IS_FROM_DOWNLOAD)) == 1,
                        downloadUrl = it.getString(it.getColumnIndexOrThrow(COLUMN_DOWNLOAD_URL)),
                        isFavorite = it.getInt(it.getColumnIndexOrThrow(COLUMN_IS_FAVORITE)) == 1,
                        playCount = it.getInt(it.getColumnIndexOrThrow(COLUMN_PLAY_COUNT)),
                        lastPlayed = it.getLong(it.getColumnIndexOrThrow(COLUMN_LAST_PLAYED)),
                        rating = it.getFloat(it.getColumnIndexOrThrow(COLUMN_RATING))
                    )
                    songs.add(song)
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("SimpleMusicDatabase", "خطأ في جلب الأغاني المحملة: ${e.message}")
        }
        
        songs
    }
    
    /**
     * البحث في الأغاني
     */
    suspend fun searchSongs(query: String): List<Song> = withContext(Dispatchers.IO) {
        val songs = mutableListOf<Song>()
        val db = readableDatabase
        
        try {
            val searchQuery = "%$query%"
            val cursor = db.query(
                TABLE_SONGS,
                null,
                "$COLUMN_TITLE LIKE ? OR $COLUMN_ARTIST LIKE ? OR $COLUMN_ALBUM LIKE ?",
                arrayOf(searchQuery, searchQuery, searchQuery),
                null,
                null,
                "$COLUMN_TITLE ASC"
            )
            
            cursor.use {
                while (it.moveToNext()) {
                    val song = Song(
                        id = it.getLong(it.getColumnIndexOrThrow(COLUMN_ID)),
                        title = it.getString(it.getColumnIndexOrThrow(COLUMN_TITLE)),
                        artist = it.getString(it.getColumnIndexOrThrow(COLUMN_ARTIST)),
                        album = it.getString(it.getColumnIndexOrThrow(COLUMN_ALBUM)),
                        duration = it.getLong(it.getColumnIndexOrThrow(COLUMN_DURATION)),
                        path = it.getString(it.getColumnIndexOrThrow(COLUMN_PATH)),
                        size = it.getLong(it.getColumnIndexOrThrow(COLUMN_SIZE)),
                        mimeType = it.getString(it.getColumnIndexOrThrow(COLUMN_MIME_TYPE)),
                        dateAdded = it.getLong(it.getColumnIndexOrThrow(COLUMN_DATE_ADDED)),
                        isFromDownload = it.getInt(it.getColumnIndexOrThrow(COLUMN_IS_FROM_DOWNLOAD)) == 1,
                        downloadUrl = it.getString(it.getColumnIndexOrThrow(COLUMN_DOWNLOAD_URL)),
                        isFavorite = it.getInt(it.getColumnIndexOrThrow(COLUMN_IS_FAVORITE)) == 1,
                        playCount = it.getInt(it.getColumnIndexOrThrow(COLUMN_PLAY_COUNT)),
                        lastPlayed = it.getLong(it.getColumnIndexOrThrow(COLUMN_LAST_PLAYED)),
                        rating = it.getFloat(it.getColumnIndexOrThrow(COLUMN_RATING))
                    )
                    songs.add(song)
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("SimpleMusicDatabase", "خطأ في البحث: ${e.message}")
        }
        
        songs
    }
    
    /**
     * حذف أغنية
     */
    suspend fun deleteSong(songId: Long): Boolean = withContext(Dispatchers.IO) {
        val db = writableDatabase
        try {
            val rowsDeleted = db.delete(TABLE_SONGS, "$COLUMN_ID = ?", arrayOf(songId.toString()))
            rowsDeleted > 0
        } catch (e: Exception) {
            android.util.Log.e("SimpleMusicDatabase", "خطأ في حذف الأغنية: ${e.message}")
            false
        }
    }
    
    /**
     * تحديث عدد مرات التشغيل
     */
    suspend fun incrementPlayCount(songId: Long): Boolean = withContext(Dispatchers.IO) {
        val db = writableDatabase
        try {
            val values = ContentValues().apply {
                put(COLUMN_LAST_PLAYED, System.currentTimeMillis())
            }
            
            db.execSQL(
                "UPDATE $TABLE_SONGS SET $COLUMN_PLAY_COUNT = $COLUMN_PLAY_COUNT + 1, $COLUMN_LAST_PLAYED = ? WHERE $COLUMN_ID = ?",
                arrayOf(System.currentTimeMillis().toString(), songId.toString())
            )
            true
        } catch (e: Exception) {
            android.util.Log.e("SimpleMusicDatabase", "خطأ في تحديث عدد التشغيل: ${e.message}")
            false
        }
    }
    
    /**
     * تحديث حالة المفضلة
     */
    suspend fun updateFavoriteStatus(songId: Long, isFavorite: Boolean): Boolean = withContext(Dispatchers.IO) {
        val db = writableDatabase
        try {
            val values = ContentValues().apply {
                put(COLUMN_IS_FAVORITE, if (isFavorite) 1 else 0)
            }
            val rowsUpdated = db.update(TABLE_SONGS, values, "$COLUMN_ID = ?", arrayOf(songId.toString()))
            rowsUpdated > 0
        } catch (e: Exception) {
            android.util.Log.e("SimpleMusicDatabase", "خطأ في تحديث المفضلة: ${e.message}")
            false
        }
    }
    
    /**
     * الحصول على إحصائيات
     */
    suspend fun getStatistics(): DatabaseStatistics = withContext(Dispatchers.IO) {
        val db = readableDatabase
        var totalSongs = 0
        var downloadedSongs = 0
        var totalSize = 0L
        
        try {
            // العدد الإجمالي
            val totalCursor = db.rawQuery("SELECT COUNT(*) FROM $TABLE_SONGS", null)
            totalCursor.use {
                if (it.moveToFirst()) {
                    totalSongs = it.getInt(0)
                }
            }
            
            // الأغاني المحملة
            val downloadedCursor = db.rawQuery("SELECT COUNT(*) FROM $TABLE_SONGS WHERE $COLUMN_IS_FROM_DOWNLOAD = 1", null)
            downloadedCursor.use {
                if (it.moveToFirst()) {
                    downloadedSongs = it.getInt(0)
                }
            }
            
            // الحجم الإجمالي
            val sizeCursor = db.rawQuery("SELECT SUM($COLUMN_SIZE) FROM $TABLE_SONGS", null)
            sizeCursor.use {
                if (it.moveToFirst()) {
                    totalSize = it.getLong(0)
                }
            }
            
        } catch (e: Exception) {
            android.util.Log.e("SimpleMusicDatabase", "خطأ في جلب الإحصائيات: ${e.message}")
        }
        
        DatabaseStatistics(totalSongs, downloadedSongs, totalSize)
    }
}

/**
 * إحصائيات قاعدة البيانات
 */
data class DatabaseStatistics(
    val totalSongs: Int,
    val downloadedSongs: Int,
    val totalSize: Long
)
