package com.musicplayer.pro.database

import android.content.Context
import androidx.room.*
import androidx.sqlite.db.SupportSQLiteDatabase
import com.musicplayer.pro.models.Song
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * قاعدة البيانات الرئيسية للتطبيق
 */
@Database(
    entities = [Song::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class MusicDatabase : RoomDatabase() {
    
    abstract fun songDao(): SongDao
    
    companion object {
        @Volatile
        private var INSTANCE: MusicDatabase? = null
        
        fun getDatabase(
            context: Context,
            scope: CoroutineScope
        ): MusicDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    MusicDatabase::class.java,
                    "music_database"
                )
                .addCallback(MusicDatabaseCallback(scope))
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
    
    private class MusicDatabaseCallback(
        private val scope: CoroutineScope
    ) : RoomDatabase.Callback() {
        
        override fun onCreate(db: SupportSQLiteDatabase) {
            super.onCreate(db)
            INSTANCE?.let { database ->
                scope.launch {
                    // يمكن إضافة بيانات أولية هنا
                    populateDatabase(database.songDao())
                }
            }
        }
        
        suspend fun populateDatabase(songDao: SongDao) {
            // مسح جميع البيانات
            songDao.deleteAll()
            
            // يمكن إضافة أغاني تجريبية هنا
        }
    }
}

/**
 * محولات الأنواع لقاعدة البيانات
 */
class Converters {
    
    @TypeConverter
    fun fromTimestamp(value: Long?): java.util.Date? {
        return value?.let { java.util.Date(it) }
    }
    
    @TypeConverter
    fun dateToTimestamp(date: java.util.Date?): Long? {
        return date?.time
    }
}
