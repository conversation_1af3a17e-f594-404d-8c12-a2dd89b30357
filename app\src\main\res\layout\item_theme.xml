<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/themeCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="?attr/colorSurface"
    app:strokeColor="?attr/colorPrimary"
    app:strokeWidth="0dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- رأس الكارد -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp">

            <!-- معاينة اللون -->
            <View
                android:id="@+id/themeColorPreview"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@drawable/color_preview_circle" />

            <!-- مؤشر التحديد -->
            <ImageView
                android:id="@+id/selectedIndicator"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_check_circle"
                android:visibility="gone"
                app:tint="?attr/colorPrimary" />

        </RelativeLayout>

        <!-- اسم الثيم -->
        <TextView
            android:id="@+id/themeName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="اسم الثيم"
            android:textAppearance="?attr/textAppearanceTitleMedium"
            android:textColor="?attr/colorOnSurface"
            android:layout_marginBottom="4dp" />

        <!-- وصف الثيم -->
        <TextView
            android:id="@+id/themeDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="وصف الثيم"
            android:textAppearance="?attr/textAppearanceBodySmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- معاينة الثيم -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- عناصر معاينة صغيرة -->
            <View
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                android:background="@drawable/preview_element_primary" />

            <View
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                android:background="@drawable/preview_element_secondary" />

            <View
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                android:background="@drawable/preview_element_tertiary" />

            <View
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:background="@drawable/preview_element_surface" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
