<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_download" modulePackage="com.musicplayer.pro" filePath="app\src\main\res\layout\item_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_download_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="211" endOffset="35"/></Target><Target id="@+id/thumbnailCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="17" startOffset="8" endLine="33" endOffset="43"/></Target><Target id="@+id/downloadThumbnail" view="ImageView"><Expressions/><location startLine="26" startOffset="12" endLine="31" endOffset="61"/></Target><Target id="@+id/downloadInfoLayout" view="LinearLayout"><Expressions/><location startLine="36" startOffset="8" endLine="103" endOffset="22"/></Target><Target id="@+id/downloadTitle" view="TextView"><Expressions/><location startLine="47" startOffset="12" endLine="56" endOffset="50"/></Target><Target id="@+id/downloadArtist" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="67" endOffset="43"/></Target><Target id="@+id/downloadStatus" view="TextView"><Expressions/><location startLine="75" startOffset="16" endLine="82" endOffset="47"/></Target><Target id="@+id/downloadQuality" view="TextView"><Expressions/><location startLine="89" startOffset="16" endLine="99" endOffset="48"/></Target><Target id="@+id/actionButton" view="ImageButton"><Expressions/><location startLine="106" startOffset="8" endLine="116" endOffset="55"/></Target><Target id="@+id/deleteButton" view="ImageButton"><Expressions/><location startLine="118" startOffset="8" endLine="127" endOffset="55"/></Target><Target id="@+id/downloadProgress" view="ProgressBar"><Expressions/><location startLine="130" startOffset="8" endLine="141" endOffset="33"/></Target><Target id="@+id/progressText" view="TextView"><Expressions/><location startLine="153" startOffset="12" endLine="159" endOffset="34"/></Target><Target id="@+id/downloadSize" view="TextView"><Expressions/><location startLine="166" startOffset="12" endLine="172" endOffset="46"/></Target><Target id="@+id/downloadSpeed" view="TextView"><Expressions/><location startLine="186" startOffset="12" endLine="192" endOffset="39"/></Target><Target id="@+id/downloadEta" view="TextView"><Expressions/><location startLine="199" startOffset="12" endLine="205" endOffset="37"/></Target></Targets></Layout>