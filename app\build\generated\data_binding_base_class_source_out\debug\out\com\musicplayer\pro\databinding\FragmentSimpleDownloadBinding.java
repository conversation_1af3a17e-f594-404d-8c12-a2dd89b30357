// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSimpleDownloadBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button downloadButton;

  @NonNull
  public final RecyclerView downloadsRecyclerView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView statusText;

  @NonNull
  public final EditText urlEditText;

  private FragmentSimpleDownloadBinding(@NonNull LinearLayout rootView,
      @NonNull Button downloadButton, @NonNull RecyclerView downloadsRecyclerView,
      @NonNull ProgressBar progressBar, @NonNull TextView statusText,
      @NonNull EditText urlEditText) {
    this.rootView = rootView;
    this.downloadButton = downloadButton;
    this.downloadsRecyclerView = downloadsRecyclerView;
    this.progressBar = progressBar;
    this.statusText = statusText;
    this.urlEditText = urlEditText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSimpleDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSimpleDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_simple_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSimpleDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.downloadButton;
      Button downloadButton = ViewBindings.findChildViewById(rootView, id);
      if (downloadButton == null) {
        break missingId;
      }

      id = R.id.downloadsRecyclerView;
      RecyclerView downloadsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (downloadsRecyclerView == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      id = R.id.urlEditText;
      EditText urlEditText = ViewBindings.findChildViewById(rootView, id);
      if (urlEditText == null) {
        break missingId;
      }

      return new FragmentSimpleDownloadBinding((LinearLayout) rootView, downloadButton,
          downloadsRecyclerView, progressBar, statusText, urlEditText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
