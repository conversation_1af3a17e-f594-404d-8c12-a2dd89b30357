<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="24dp"
    android:background="@color/surface_primary">

    <!-- العنوان -->
    <TextView
        android:id="@+id/dialogTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/add_download"
        android:textColor="@color/text_primary"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- حقل الرابط -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/urlInputLayout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:hint="رابط الفيديو أو الصوت"
        app:startIconDrawable="@drawable/ic_link"
        app:endIconMode="clear_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dialogTitle">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/urlEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textUri"
            android:maxLines="3" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- اختيار الجودة -->
    <TextView
        android:id="@+id/qualityLabel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="الجودة:"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/urlInputLayout" />

    <Spinner
        android:id="@+id/qualitySpinner"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/spinner_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/qualityLabel" />

    <!-- اختيار التنسيق -->
    <TextView
        android:id="@+id/formatLabel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="التنسيق:"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/qualitySpinner" />

    <Spinner
        android:id="@+id/formatSpinner"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/spinner_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/formatLabel" />

    <!-- الأزرار -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="end"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/formatSpinner">

        <Button
            android:id="@+id/cancelButton"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="@string/cancel"
            android:textColor="@color/text_secondary" />

        <Button
            android:id="@+id/downloadButton"
            style="@style/Widget.MaterialComponents.Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/download"
            android:backgroundTint="@color/accent_primary"
            android:textColor="@color/white" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
