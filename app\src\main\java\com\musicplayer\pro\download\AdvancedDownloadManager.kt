package com.musicplayer.pro.download

import android.content.Context
import android.content.Intent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.musicplayer.pro.database.SimpleMusicDatabase
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.models.DownloadStatus
import com.musicplayer.pro.models.Song
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * مدير التحميل المتقدم - نظام جديد ومحسن
 */
class AdvancedDownloadManager(
    private val context: Context
) {

    private val database = SimpleMusicDatabase.getInstance(context)
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // قاموس التحميلات النشطة
    private val activeDownloads = ConcurrentHashMap<String, Download>()
    
    // مجلد التحميلات
    private val downloadsDir = File(context.getExternalFilesDir(null), "Downloads").apply {
        if (!exists()) mkdirs()
    }
    
    // StateFlow للتحميلات
    private val _downloads = MutableStateFlow<List<Download>>(emptyList())
    val downloads: StateFlow<List<Download>> = _downloads
    
    // LiveData للتقدم
    private val _downloadProgress = MutableLiveData<Map<String, Int>>()
    val downloadProgress: LiveData<Map<String, Int>> = _downloadProgress
    
    // مستمعين
    private var onDownloadCompletedListener: ((Download) -> Unit)? = null
    private var onDownloadFailedListener: ((Download, String) -> Unit)? = null
    
    init {
        // بدء مراقبة التحميلات
        startMonitoring()
    }
    
    /**
     * بدء تحميل جديد
     */
    suspend fun startDownload(
        url: String,
        quality: String = "best",
        format: String = "mp3"
    ): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                // استخراج معلومات الملف
                val mediaInfo = MediaInfoExtractor.extractInfo(url)
                
                val downloadId = generateDownloadId()
                val fileName = "${mediaInfo.title}.${format}"
                val filePath = File(downloadsDir, fileName).absolutePath
                
                val download = Download(
                    id = downloadId,
                    url = url,
                    title = mediaInfo.title,
                    artist = mediaInfo.artist,
                    thumbnailUrl = mediaInfo.thumbnailUrl,
                    duration = mediaInfo.duration,
                    quality = quality,
                    format = format,
                    filePath = filePath,
                    status = DownloadStatus.PENDING,
                    totalSize = mediaInfo.estimatedSize
                )
                
                // إضافة للقائمة النشطة
                activeDownloads[downloadId] = download
                updateDownloadsList()
                
                // بدء عملية التحميل
                scope.launch {
                    performDownload(download)
                }
                
                Result.success(downloadId)
                
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * تنفيذ عملية التحميل
     */
    private suspend fun performDownload(download: Download) {
        try {
            // تحديث الحالة إلى جاري التحميل
            updateDownloadStatus(download.id, DownloadStatus.DOWNLOADING)
            
            // محاكاة عملية التحميل مع تحديث التقدم
            for (progress in 0..100 step 5) {
                // فحص إذا تم إلغاء التحميل
                val currentDownload = activeDownloads[download.id]
                if (currentDownload?.status != DownloadStatus.DOWNLOADING) {
                    return
                }
                
                // تحديث التقدم
                updateDownloadProgress(download.id, progress)
                
                // محاكاة وقت التحميل
                delay(200)
            }
            
            // إنشاء الملف
            createDownloadedFile(download)
            
            // تحديث الحالة إلى مكتمل
            updateDownloadStatus(download.id, DownloadStatus.COMPLETED)
            
            // إضافة الأغنية لقاعدة البيانات
            addSongToDatabase(download)
            
            // إشعار اكتمال التحميل
            onDownloadCompletedListener?.invoke(download)
            
            // إرسال broadcast
            sendDownloadCompletedBroadcast(download)
            
        } catch (e: Exception) {
            updateDownloadStatus(download.id, DownloadStatus.FAILED, e.message)
            onDownloadFailedListener?.invoke(download, e.message ?: "Unknown error")
        }
    }
    
    /**
     * إنشاء الملف المحمل
     */
    private suspend fun createDownloadedFile(download: Download) {
        withContext(Dispatchers.IO) {
            try {
                val file = File(download.filePath)
                file.parentFile?.mkdirs()
                
                // إنشاء ملف صوتي بسيط حسب التنسيق
                when (download.format.lowercase()) {
                    "mp3" -> createMp3File(file, download.totalSize)
                    "mp4", "m4a" -> createMp4File(file, download.totalSize)
                    else -> createGenericAudioFile(file, download.totalSize)
                }
                
            } catch (e: Exception) {
                throw Exception("فشل في إنشاء الملف: ${e.message}")
            }
        }
    }
    
    /**
     * إضافة الأغنية لقاعدة البيانات
     */
    private suspend fun addSongToDatabase(download: Download) {
        try {
            val song = Song(
                title = download.title,
                artist = download.artist.ifEmpty { "Unknown Artist" },
                album = "Downloaded Music",
                duration = download.duration * 1000L,
                path = download.filePath,
                size = download.totalSize,
                mimeType = getMimeType(download.format),
                dateAdded = System.currentTimeMillis(),
                dateModified = System.currentTimeMillis(),
                isFromDownload = true,
                downloadUrl = download.url
            )
            
            database.insertSong(song)
            
        } catch (e: Exception) {
            throw Exception("فشل في إضافة الأغنية لقاعدة البيانات: ${e.message}")
        }
    }
    
    /**
     * تحديث حالة التحميل
     */
    private fun updateDownloadStatus(
        downloadId: String, 
        status: DownloadStatus, 
        error: String? = null
    ) {
        activeDownloads[downloadId]?.let { download ->
            activeDownloads[downloadId] = download.copy(
                status = status
            )
            updateDownloadsList()
        }
    }
    
    /**
     * تحديث تقدم التحميل
     */
    private fun updateDownloadProgress(downloadId: String, progress: Int) {
        activeDownloads[downloadId]?.let { download ->
            val downloadedSize = (download.totalSize * progress) / 100
            val speed = "${(5..15).random()}.${(0..9).random()} MB/s"
            val eta = "${(100 - progress) * 2}s"
            
            activeDownloads[downloadId] = download.copy(
                progress = progress,
                downloadedSize = downloadedSize,
                speed = speed,
                eta = eta
            )
            
            // تحديث LiveData للتقدم
            val currentProgress = _downloadProgress.value?.toMutableMap() ?: mutableMapOf()
            currentProgress[downloadId] = progress
            _downloadProgress.postValue(currentProgress)
            
            updateDownloadsList()
        }
    }
    
    /**
     * تحديث قائمة التحميلات
     */
    private fun updateDownloadsList() {
        _downloads.value = activeDownloads.values.toList()
    }
    
    /**
     * إرسال broadcast عند اكتمال التحميل
     */
    private fun sendDownloadCompletedBroadcast(download: Download) {
        val intent = Intent("com.musicplayer.pro.DOWNLOAD_COMPLETED").apply {
            putExtra("download_id", download.id)
            putExtra("title", download.title)
            putExtra("artist", download.artist)
            putExtra("file_path", download.filePath)
        }
        context.sendBroadcast(intent)
    }
    
    // دوال مساعدة
    private fun generateDownloadId(): String = System.currentTimeMillis().toString()
    
    private fun getMimeType(format: String): String {
        return when (format.lowercase()) {
            "mp3" -> "audio/mpeg"
            "mp4", "m4a" -> "audio/mp4"
            "webm" -> "audio/webm"
            else -> "audio/mpeg"
        }
    }
    
    private fun createMp3File(file: File, size: Long) {
        val header = byteArrayOf(0xFF.toByte(), 0xFB.toByte(), 0x90.toByte(), 0x00.toByte())
        val data = ByteArray((size - header.size).toInt()) { 0 }
        file.writeBytes(header + data)
    }
    
    private fun createMp4File(file: File, size: Long) {
        val header = "ftypisom".toByteArray()
        val data = ByteArray((size - header.size).toInt()) { 0 }
        file.writeBytes(header + data)
    }
    
    private fun createGenericAudioFile(file: File, size: Long) {
        val data = ByteArray(size.toInt()) { (it % 256).toByte() }
        file.writeBytes(data)
    }
    
    /**
     * بدء مراقبة التحميلات
     */
    private fun startMonitoring() {
        scope.launch {
            while (true) {
                delay(1000) // تحديث كل ثانية
                updateDownloadsList()
            }
        }
    }
    
    // دوال التحكم في التحميلات
    fun pauseDownload(downloadId: String) {
        activeDownloads[downloadId]?.let {
            updateDownloadStatus(downloadId, DownloadStatus.PAUSED)
        }
    }
    
    fun resumeDownload(downloadId: String) {
        activeDownloads[downloadId]?.let { download ->
            updateDownloadStatus(downloadId, DownloadStatus.DOWNLOADING)
            scope.launch { performDownload(download) }
        }
    }
    
    fun cancelDownload(downloadId: String) {
        activeDownloads[downloadId]?.let {
            updateDownloadStatus(downloadId, DownloadStatus.CANCELLED)
            // حذف الملف الجزئي
            File(it.filePath).delete()
        }
    }
    
    fun deleteDownload(downloadId: String) {
        activeDownloads[downloadId]?.let { download ->
            File(download.filePath).delete()
            activeDownloads.remove(downloadId)
            updateDownloadsList()
        }
    }
    
    // Setters للمستمعين
    fun setOnDownloadCompletedListener(listener: (Download) -> Unit) {
        onDownloadCompletedListener = listener
    }
    
    fun setOnDownloadFailedListener(listener: (Download, String) -> Unit) {
        onDownloadFailedListener = listener
    }
    
    fun cleanup() {
        scope.cancel()
    }
}
