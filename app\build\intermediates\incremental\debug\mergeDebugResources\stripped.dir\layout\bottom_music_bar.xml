<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:background="@drawable/bottom_bar_background"
    android:elevation="12dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:paddingTop="4dp"
    android:paddingBottom="4dp">

    <!-- شريط التقدم المحسن -->
    <ProgressBar
        android:id="@+id/miniProgressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="0dp"
        android:layout_height="3dp"
        android:progressTint="@color/accent_primary"
        android:progressBackgroundTint="@color/surface_secondary"
        android:alpha="0.8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:progress="45" />

    <!-- صورة الغلاف المحسنة -->
    <androidx.cardview.widget.CardView
        android:id="@+id/miniAlbumCoverCard"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/miniProgressBar">

        <ImageView
            android:id="@+id/miniAlbumCover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/default_album_cover"
            android:contentDescription="@string/album_cover" />

        <!-- تأثير التشغيل -->
        <View
            android:id="@+id/playingIndicator"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/playing_overlay"
            android:visibility="gone" />

    </androidx.cardview.widget.CardView>

    <!-- معلومات الأغنية المحسنة -->
    <LinearLayout
        android:id="@+id/songInfoLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="vertical"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnMiniPlayPause"
        app:layout_constraintStart_toEndOf="@+id/miniAlbumCoverCard"
        app:layout_constraintTop_toBottomOf="@+id/miniProgressBar">

        <TextView
            android:id="@+id/miniSongTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="@color/text_primary"
            android:textSize="17sp"
            android:textStyle="bold"
            android:layout_marginBottom="3dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:scrollHorizontally="true"
            tools:text="Beautiful Song Title That Might Be Long" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/miniArtistName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                tools:text="Amazing Artist" />

            <!-- مؤشر الجودة -->
            <TextView
                android:id="@+id/qualityIndicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:background="@drawable/quality_badge"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:text="HD"
                android:textColor="@color/accent_primary"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

    <!-- زر التشغيل/الإيقاف المحسن -->
    <ImageButton
        android:id="@+id/btnMiniPlayPause"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/play_button_background"
        android:contentDescription="@string/play_pause"
        android:src="@drawable/ic_play"
        android:tint="@android:color/white"
        android:elevation="6dp"
        android:scaleType="centerInside"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/miniProgressBar" />

</androidx.constraintlayout.widget.ConstraintLayout>
