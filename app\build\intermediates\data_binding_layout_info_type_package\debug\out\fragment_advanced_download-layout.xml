<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_advanced_download" modulePackage="com.musicplayer.pro" filePath="app\src\main\res\layout\fragment_advanced_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_advanced_download_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="187" endOffset="53"/></Target><Target id="@+id/urlEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="46" startOffset="20" endLine="52" endOffset="65"/></Target><Target id="@+id/qualitySpinner" view="Spinner"><Expressions/><location startLine="79" startOffset="24" endLine="83" endOffset="79"/></Target><Target id="@+id/formatSpinner" view="Spinner"><Expressions/><location startLine="103" startOffset="24" endLine="107" endOffset="79"/></Target><Target id="@+id/downloadButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="114" startOffset="16" endLine="126" endOffset="43"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="133" startOffset="8" endLine="140" endOffset="58"/></Target><Target id="@+id/statusTextView" view="TextView"><Expressions/><location startLine="143" startOffset="8" endLine="151" endOffset="42"/></Target><Target id="@+id/statsTextView" view="TextView"><Expressions/><location startLine="154" startOffset="8" endLine="162" endOffset="68"/></Target><Target id="@+id/downloadsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="165" startOffset="8" endLine="172" endOffset="61"/></Target><Target id="@+id/fab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="177" startOffset="4" endLine="185" endOffset="42"/></Target></Targets></Layout>