<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- Base Modern Theme - Light -->
    <style name="Theme.MusicPlayer.Modern" parent="Theme.Material3.DayNight">
        <!-- Primary Colors -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        
        <!-- Secondary Colors -->
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        
        <!-- Tertiary Colors -->
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        
        <!-- Error Colors -->
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        
        <!-- Background Colors -->
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        
        <!-- Surface Variants -->
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        
        <!-- Status Bar -->
        <item name="android:statusBarColor">@color/md_theme_light_surface</item>
        <item name="android:windowLightStatusBar">true</item>
        
        <!-- Navigation Bar -->
        <item name="android:navigationBarColor">@color/md_theme_light_surface</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- Window -->
        <item name="android:windowBackground">@color/md_theme_light_background</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
    </style>
    
    <!-- Purple Music Theme -->
    <style name="Theme.MusicPlayer.Purple" parent="Theme.MusicPlayer.Modern">
        <item name="colorPrimary">@color/music_accent_purple</item>
        <item name="colorPrimaryContainer">#F3E8FF</item>
        <item name="colorSecondary">#A855F7</item>
        <item name="colorSecondaryContainer">#F3E8FF</item>
        <item name="colorTertiary">#C084FC</item>
        <item name="colorTertiaryContainer">#FAF5FF</item>
    </style>
    
    <!-- Blue Music Theme -->
    <style name="Theme.MusicPlayer.Blue" parent="Theme.MusicPlayer.Modern">
        <item name="colorPrimary">@color/music_accent_blue</item>
        <item name="colorPrimaryContainer">#DBEAFE</item>
        <item name="colorSecondary">#2563EB</item>
        <item name="colorSecondaryContainer">#DBEAFE</item>
        <item name="colorTertiary">#60A5FA</item>
        <item name="colorTertiaryContainer">#EFF6FF</item>
    </style>
    
    <!-- Green Music Theme -->
    <style name="Theme.MusicPlayer.Green" parent="Theme.MusicPlayer.Modern">
        <item name="colorPrimary">@color/music_accent_green</item>
        <item name="colorPrimaryContainer">#D1FAE5</item>
        <item name="colorSecondary">#059669</item>
        <item name="colorSecondaryContainer">#D1FAE5</item>
        <item name="colorTertiary">#34D399</item>
        <item name="colorTertiaryContainer">#ECFDF5</item>
    </style>
    
    <!-- Pink Music Theme -->
    <style name="Theme.MusicPlayer.Pink" parent="Theme.MusicPlayer.Modern">
        <item name="colorPrimary">@color/music_accent_pink</item>
        <item name="colorPrimaryContainer">#FCE7F3</item>
        <item name="colorSecondary">#DB2777</item>
        <item name="colorSecondaryContainer">#FCE7F3</item>
        <item name="colorTertiary">#F472B6</item>
        <item name="colorTertiaryContainer">#FDF2F8</item>
    </style>
    
    <!-- Orange Music Theme -->
    <style name="Theme.MusicPlayer.Orange" parent="Theme.MusicPlayer.Modern">
        <item name="colorPrimary">@color/music_accent_orange</item>
        <item name="colorPrimaryContainer">#FEF3C7</item>
        <item name="colorSecondary">#D97706</item>
        <item name="colorSecondaryContainer">#FEF3C7</item>
        <item name="colorTertiary">#FBBF24</item>
        <item name="colorTertiaryContainer">#FFFBEB</item>
    </style>
    
    <!-- Dark Theme Base -->
    <style name="Theme.MusicPlayer.Dark" parent="Theme.Material3.DayNight">
        <!-- Primary Colors -->
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_onPrimaryContainer</item>
        
        <!-- Secondary Colors -->
        <item name="colorSecondary">@color/md_theme_dark_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_dark_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_dark_onSecondaryContainer</item>
        
        <!-- Tertiary Colors -->
        <item name="colorTertiary">@color/md_theme_dark_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_dark_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_dark_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_dark_onTertiaryContainer</item>
        
        <!-- Error Colors -->
        <item name="colorError">@color/md_theme_dark_error</item>
        <item name="colorOnError">@color/md_theme_dark_onError</item>
        <item name="colorErrorContainer">@color/md_theme_dark_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_dark_onErrorContainer</item>
        
        <!-- Background Colors -->
        <item name="android:colorBackground">@color/md_theme_dark_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_onBackground</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_onSurface</item>
        
        <!-- Surface Variants -->
        <item name="colorSurfaceVariant">@color/md_theme_dark_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_dark_outline</item>
        
        <!-- Status Bar -->
        <item name="android:statusBarColor">@color/md_theme_dark_surface</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation Bar -->
        <item name="android:navigationBarColor">@color/md_theme_dark_surface</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        <!-- Window -->
        <item name="android:windowBackground">@color/md_theme_dark_background</item>
    </style>
    
    <!-- AMOLED Black Theme -->
    <style name="Theme.MusicPlayer.AMOLED" parent="Theme.MusicPlayer.Dark">
        <item name="android:colorBackground">#000000</item>
        <item name="colorSurface">#000000</item>
        <item name="android:windowBackground">#000000</item>
        <item name="android:statusBarColor">#000000</item>
        <item name="android:navigationBarColor">#000000</item>
    </style>
    
    <!-- Glass Effect Theme -->
    <style name="Theme.MusicPlayer.Glass" parent="Theme.MusicPlayer.Modern">
        <item name="colorSurface">@color/glass_surface</item>
        <item name="colorSurfaceVariant">@color/glass_surface</item>
        <item name="android:windowBackground">@drawable/gradient_background</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>
    
    <!-- Component Styles -->
    
    <!-- Modern Card Style -->
    <style name="Widget.MusicPlayer.Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">8dp</item>
        <item name="cardBackgroundColor">?attr/colorSurface</item>
        <item name="strokeWidth">0dp</item>
    </style>
    
    <!-- Modern Button Style -->
    <style name="Widget.MusicPlayer.Button" parent="Widget.Material3.Button">
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="android:letterSpacing">0.01</item>
    </style>
    
    <!-- Modern FAB Style -->
    <style name="Widget.MusicPlayer.FloatingActionButton" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="shapeAppearance">@style/ShapeAppearance.MusicPlayer.SmallComponent</item>
        <item name="elevation">12dp</item>
    </style>
    
    <!-- Shape Appearances -->
    <style name="ShapeAppearance.MusicPlayer.SmallComponent" parent="ShapeAppearance.Material3.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>
    
    <style name="ShapeAppearance.MusicPlayer.MediumComponent" parent="ShapeAppearance.Material3.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style>
    
    <!-- Text Appearances -->
    <style name="TextAppearance.MusicPlayer.Headline" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="android:letterSpacing">-0.02</item>
    </style>
    
    <style name="TextAppearance.MusicPlayer.Title" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="android:letterSpacing">0</item>
    </style>
    
    <style name="TextAppearance.MusicPlayer.Body" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:fontFamily">@font/roboto_regular</item>
        <item name="android:letterSpacing">0.01</item>
    </style>
    
</resources>
