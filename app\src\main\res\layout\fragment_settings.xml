<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- عنوان الصفحة -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="الإعدادات"
            android:textAppearance="?attr/textAppearanceHeadlineMedium"
            android:textColor="?attr/colorOnBackground"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- إعدادات الثيمات -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/themeSettingsCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="?attr/colorSurface"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <!-- أيقونة -->
                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="16dp"
                    android:src="@drawable/ic_palette"
                    android:background="@drawable/icon_background_circle"
                    android:padding="12dp"
                    app:tint="?attr/colorPrimary" />

                <!-- النص -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الثيمات والألوان"
                        android:textAppearance="?attr/textAppearanceTitleMedium"
                        android:textColor="?attr/colorOnSurface" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تخصيص مظهر التطبيق والألوان"
                        android:textAppearance="?attr/textAppearanceBodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

                <!-- سهم -->
                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_arrow_forward"
                    app:tint="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- إعدادات أخرى يمكن إضافتها لاحقاً -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="المزيد من الإعدادات قريباً..."
            android:textAppearance="?attr/textAppearanceBodyMedium"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:gravity="center"
            android:layout_marginTop="32dp" />

    </LinearLayout>

</ScrollView>
