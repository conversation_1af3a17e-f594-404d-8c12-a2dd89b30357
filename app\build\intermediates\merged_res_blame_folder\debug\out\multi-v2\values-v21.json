{"logs": [{"outputFile": "com.musicplayer.pro.app-mergeDebugResources-59:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\210f3162f1672bd532c02d8af1bd50c1\\transformed\\preference-1.2.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,6", "startColumns": "4,4,4", "startOffsets": "55,120,276", "endLines": "2,5,8", "endColumns": "64,12,12", "endOffsets": "115,271,449"}, "to": {"startLines": "21,354,357", "startColumns": "4,4,4", "startOffsets": "1662,25969,26125", "endLines": "21,356,359", "endColumns": "64,12,12", "endOffsets": "1722,26120,26298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d0962e3cd95d785309666b77b5473090\\transformed\\appcompat-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,41,43,44,45,46,48,50,51,52,53,54,56,58,60,62,64,66,67,72,74,76,77,78,80,82,83,84,85,90,102,145,148,191,206,218,220,222,224,227,231,234,235,236,239,240,241,242,243,244,247,248,250,252,254,256,260,262,263,264,265,267,271,273,275,276,277,278,279,280,316,317,318,328,329,330,342", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2099,2190,2293,2396,2501,2608,2717,2826,2935,3044,3153,3260,3363,3482,3637,3792,3897,4018,4119,4266,4407,4510,4629,4736,4839,4994,5165,5314,5479,5636,5787,5906,6257,6406,6555,6667,6814,6967,7114,7189,7278,7365,7890,8982,11740,11925,14695,15828,16680,16803,16926,17039,17222,17477,17678,17767,17878,18111,18212,18307,18430,18559,18676,18853,18952,19087,19230,19365,19484,19685,19804,19897,20008,20064,20171,20366,20477,20610,20705,20796,20887,20980,21097,23817,23888,23971,24594,24651,24709,25333", "endLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,40,42,43,44,45,47,49,50,51,52,53,55,57,59,61,63,65,66,71,73,75,76,77,79,81,82,83,84,85,90,144,147,190,193,208,219,221,223,226,230,233,234,235,238,239,240,241,242,243,246,247,249,251,253,255,259,261,262,263,264,266,270,272,274,275,276,277,278,279,281,316,317,327,328,329,341,353", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "2185,2288,2391,2496,2603,2712,2821,2930,3039,3148,3255,3358,3477,3632,3787,3892,4013,4114,4261,4402,4505,4624,4731,4834,4989,5160,5309,5474,5631,5782,5901,6252,6401,6550,6662,6809,6962,7109,7184,7273,7360,7461,7988,11735,11920,14690,14887,16022,16798,16921,17034,17217,17472,17673,17762,17873,18106,18207,18302,18425,18554,18671,18848,18947,19082,19225,19360,19479,19680,19799,19892,20003,20059,20166,20361,20472,20605,20700,20791,20882,20975,21092,21231,23883,23966,24589,24646,24704,25328,25964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e081d70774c49b9b7dc6950692deb52\\transformed\\material-1.11.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,29,32,35,38,41,44,47,50,53,56,59,60,63,68,79,85,94,103,112,121,130,139,148,157,166,175,184,193,202,211,220,226,232,238,244,248,252,253,254,255,259,262,265,268,271,272,275,278,282,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1386,1476,1572,1662,1755,1862,1967,2086,2211,2332,2545,2804,3075,3293,3525,3761,4011,4224,4433,4664,4865,4981,5151,5472,6501,6958,7462,7970,8479,8993,9498,10002,10507,11013,11515,12021,12530,13038,13537,14044,14552,14844,15138,15438,15738,16067,16408,16546,16690,16846,17239,17457,17679,17905,18121,18231,18401,18591,18832,19091", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,28,31,34,37,40,43,46,49,52,55,58,59,62,67,78,84,93,102,111,120,129,138,147,156,165,174,183,192,201,210,219,225,231,237,243,247,251,252,253,254,258,261,264,267,270,271,274,277,281,285,288", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1381,1471,1567,1657,1750,1857,1962,2081,2206,2327,2540,2799,3070,3288,3520,3756,4006,4219,4428,4659,4860,4976,5146,5467,6496,6953,7457,7965,8474,8988,9493,9997,10502,11008,11510,12016,12525,13033,13532,14039,14547,14839,15133,15433,15733,16062,16403,16541,16685,16841,17234,17452,17674,17900,18116,18226,18396,18586,18827,19086,19263"}, "to": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,22,23,24,25,86,87,88,89,91,92,93,96,99,194,197,200,203,209,212,215,282,285,286,289,299,310,376,385,394,403,412,421,430,439,448,457,466,475,484,493,502,511,517,523,529,535,539,543,544,545,546,550,553,556,559,570,571,574,577,581,585", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "232,330,426,522,620,688,767,855,943,1031,1119,1206,1293,1380,1727,1823,1913,2009,7466,7559,7666,7771,7993,8118,8239,8452,8711,14892,15110,15342,15578,16027,16240,16449,21236,21437,21553,21723,22331,23360,27465,27969,28477,28986,29500,30005,30509,31014,31520,32022,32528,33037,33545,34044,34551,35059,35351,35645,35945,36245,36574,36915,37053,37197,37353,37746,37964,38186,38412,39152,39262,39432,39622,39863,40122", "endLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,22,23,24,25,86,87,88,89,91,92,95,98,101,196,199,202,205,211,214,217,284,285,288,293,309,315,384,393,402,411,420,429,438,447,456,465,474,483,492,501,510,516,522,528,534,538,542,543,544,545,549,552,555,558,561,570,573,576,580,584,587", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "325,421,517,615,683,762,850,938,1026,1114,1201,1288,1375,1462,1818,1908,2004,2094,7554,7661,7766,7885,8113,8234,8447,8706,8977,15105,15337,15573,15823,16235,16444,16675,21432,21548,21718,22039,23355,23812,27964,28472,28981,29495,30000,30504,31009,31515,32017,32523,33032,33540,34039,34546,35054,35346,35640,35940,36240,36569,36910,37048,37192,37348,37741,37959,38181,38407,38623,39257,39427,39617,39858,40117,40294"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3e6d558491d75611da77b86b8bf4ede4\\transformed\\media-1.7.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "362,365,369,373", "startColumns": "4,4,4,4", "startOffsets": "26545,26713,27002,27298", "endLines": "364,367,371,375", "endColumns": "12,12,12,12", "endOffsets": "26708,26871,27165,27460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7c108dd56627f30fe94755d1a2faeaf2\\transformed\\core-1.12.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,18,19,20,360,361,368,372,562,565", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1467,1531,1598,26303,26419,26876,27170,38628,38800", "endLines": "2,18,19,20,360,361,368,372,564,569", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1526,1593,1657,26414,26540,26997,27293,38795,39147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\472040ed58573b5e54239e60b254baef\\transformed\\jetified-media3-ui-1.2.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,114", "endLines": "2,7", "endColumns": "58,10", "endOffsets": "109,396"}, "to": {"startLines": "3,294", "startColumns": "4,4", "startOffsets": "173,22044", "endLines": "3,298", "endColumns": "58,10", "endOffsets": "227,22326"}}]}]}