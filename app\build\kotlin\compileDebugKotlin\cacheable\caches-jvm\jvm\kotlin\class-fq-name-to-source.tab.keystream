%com.musicplayer.pro.EqualizerActivity com.musicplayer.pro.MainActivity*com.musicplayer.pro.MainActivity.Companion1com.musicplayer.pro.MainActivity.MainPagerAdapter*com.musicplayer.pro.MusicPlayerApplication$com.musicplayer.pro.SettingsActivity5com.musicplayer.pro.SettingsActivity.SettingsFragment4com.musicplayer.pro.adapters.AdvancedDownloadAdapterOcom.musicplayer.pro.adapters.AdvancedDownloadAdapter.AdvancedDownloadViewHolder,com.musicplayer.pro.adapters.DownloadAdapter?com.musicplayer.pro.adapters.DownloadAdapter.DownloadViewHolder-com.musicplayer.pro.adapters.EqualizerAdapter<com.musicplayer.pro.adapters.EqualizerAdapter.BandViewHolder(com.musicplayer.pro.adapters.SongAdapter7com.musicplayer.pro.adapters.SongAdapter.SongViewHolder0com.musicplayer.pro.database.SimpleMusicDatabase:com.musicplayer.pro.database.SimpleMusicDatabase.Companion/com.musicplayer.pro.database.DatabaseStatistics4com.musicplayer.pro.download.AdvancedDownloadManager/com.musicplayer.pro.download.MediaInfoExtractor&com.musicplayer.pro.download.MediaInfo6com.musicplayer.pro.fragments.AdvancedDownloadFragment.com.musicplayer.pro.fragments.DownloadFragment*com.musicplayer.pro.fragments.MainFragment0com.musicplayer.pro.fragments.NowPlayingFragment0com.musicplayer.pro.managers.AudioEffectsManager*com.musicplayer.pro.managers.BackupManager)com.musicplayer.pro.managers.CacheManager&com.musicplayer.pro.managers.CacheType)com.musicplayer.pro.managers.CacheDetails,com.musicplayer.pro.managers.DownloadManager6com.musicplayer.pro.managers.DownloadManager.VideoInfo#com.musicplayer.pro.models.Download)com.musicplayer.pro.models.DownloadStatus(com.musicplayer.pro.models.EqualizerBand#com.musicplayer.pro.models.Playlist'com.musicplayer.pro.models.PlaylistType%com.musicplayer.pro.models.RepeatModecom.musicplayer.pro.models.Song8com.musicplayer.pro.repositories.AdvancedMusicRepository0com.musicplayer.pro.repositories.MusicStatistics0com.musicplayer.pro.repositories.MusicRepository)com.musicplayer.pro.services.MusicService3com.musicplayer.pro.services.MusicService.Companion5com.musicplayer.pro.services.MusicService.MusicBinder%com.musicplayer.pro.utils.ImageLoader&com.musicplayer.pro.utils.MediaScanner0com.musicplayer.pro.utils.MediaScanner.Companion+com.musicplayer.pro.utils.PermissionManager&com.musicplayer.pro.utils.ThemeManager8com.musicplayer.pro.viewmodels.AdvancedDownloadViewModel1com.musicplayer.pro.viewmodels.DownloadStatistics0com.musicplayer.pro.viewmodels.DownloadViewModel:com.musicplayer.pro.viewmodels.DownloadViewModel.MediaInfo-com.musicplayer.pro.viewmodels.MusicViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     