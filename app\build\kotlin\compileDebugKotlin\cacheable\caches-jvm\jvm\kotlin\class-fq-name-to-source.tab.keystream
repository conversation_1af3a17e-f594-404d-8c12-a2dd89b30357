*com.musicplayer.pro.MusicPlayerApplication&com.musicplayer.pro.SimpleMainActivity com.musicplayer.pro.MainActivity*com.musicplayer.pro.MainActivity.Companion1com.musicplayer.pro.MainActivity.MainPagerAdapter(com.musicplayer.pro.adapters.SongAdapter7com.musicplayer.pro.adapters.SongAdapter.SongViewHolder.com.musicplayer.pro.fragments.DownloadFragment*com.musicplayer.pro.fragments.MainFragment0com.musicplayer.pro.fragments.NowPlayingFragment#com.musicplayer.pro.models.Playlist'com.musicplayer.pro.models.PlaylistType#com.musicplayer.pro.models.Download)com.musicplayer.pro.models.DownloadStatus%com.musicplayer.pro.models.RepeatModecom.musicplayer.pro.models.Song0com.musicplayer.pro.repositories.MusicRepository)com.musicplayer.pro.services.MusicService5com.musicplayer.pro.services.MusicService.MusicBinder%com.musicplayer.pro.utils.ImageLoader&com.musicplayer.pro.utils.MediaScanner+com.musicplayer.pro.utils.PermissionManager&com.musicplayer.pro.utils.ThemeManager-com.musicplayer.pro.viewmodels.MusicViewModel9com.musicplayer.pro.databinding.FragmentNowPlayingBinding/com.musicplayer.pro.databinding.ItemSongBinding3com.musicplayer.pro.databinding.ActivityMainBinding5com.musicplayer.pro.databinding.BottomMusicBarBinding7com.musicplayer.pro.databinding.FragmentDownloadBinding3com.musicplayer.pro.databinding.FragmentMainBinding,com.musicplayer.pro.adapters.DownloadAdapter?com.musicplayer.pro.adapters.DownloadAdapter.DownloadViewHolder,com.musicplayer.pro.managers.DownloadManager6com.musicplayer.pro.managers.DownloadManager.VideoInfo0com.musicplayer.pro.viewmodels.DownloadViewModel8com.musicplayer.pro.databinding.DialogAddDownloadBinding3com.musicplayer.pro.databinding.ItemDownloadBinding3com.musicplayer.pro.services.MusicService.Companion$com.musicplayer.pro.SettingsActivity5com.musicplayer.pro.SettingsActivity.SettingsFragment0com.musicplayer.pro.managers.AudioEffectsManager*com.musicplayer.pro.managers.BackupManager)com.musicplayer.pro.managers.CacheManager&com.musicplayer.pro.managers.CacheType)com.musicplayer.pro.managers.CacheDetails7com.musicplayer.pro.databinding.ActivitySettingsBinding%com.musicplayer.pro.EqualizerActivity-com.musicplayer.pro.adapters.EqualizerAdapter<com.musicplayer.pro.adapters.EqualizerAdapter.BandViewHolder(com.musicplayer.pro.models.EqualizerBand8com.musicplayer.pro.databinding.ActivityEqualizerBinding8com.musicplayer.pro.databinding.ItemEqualizerBandBinding0com.musicplayer.pro.utils.MediaScanner.Companion:com.musicplayer.pro.viewmodels.DownloadViewModel.MediaInfo                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                