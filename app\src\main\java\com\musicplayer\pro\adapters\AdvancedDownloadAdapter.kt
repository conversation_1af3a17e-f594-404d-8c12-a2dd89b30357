package com.musicplayer.pro.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.recyclerview.widget.RecyclerView
import com.musicplayer.pro.R
import com.musicplayer.pro.models.Download
import com.musicplayer.pro.models.DownloadStatus

/**
 * Adapter متقدم للتحميلات مع واجهة محسنة
 */
class AdvancedDownloadAdapter(
    private val onDownloadClick: (Download) -> Unit,
    private val onActionClick: (Download, String) -> Unit,
    private val onDeleteClick: (Download) -> Unit
) : RecyclerView.Adapter<AdvancedDownloadAdapter.AdvancedDownloadViewHolder>() {
    
    private var downloads = listOf<Download>()
    private var progressMap = mapOf<String, Int>()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AdvancedDownloadViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_advanced_download, parent, false)
        return AdvancedDownloadViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: AdvancedDownloadViewHolder, position: Int) {
        holder.bind(downloads[position])
    }
    
    override fun getItemCount(): Int = downloads.size
    
    /**
     * تحديث قائمة التحميلات
     */
    fun updateDownloads(newDownloads: List<Download>) {
        downloads = newDownloads
        notifyDataSetChanged()
    }
    
    /**
     * تحديث تقدم التحميلات
     */
    fun updateProgress(newProgressMap: Map<String, Int>) {
        progressMap = newProgressMap
        // تحديث العناصر المرئية فقط
        for (i in 0 until itemCount) {
            val download = downloads.getOrNull(i)
            if (download != null && progressMap.containsKey(download.id)) {
                notifyItemChanged(i)
            }
        }
    }
    
    inner class AdvancedDownloadViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        
        // Views
        private val thumbnailImageView: ImageView = itemView.findViewById(R.id.downloadThumbnail)
        private val titleTextView: TextView = itemView.findViewById(R.id.downloadTitle)
        private val artistTextView: TextView = itemView.findViewById(R.id.downloadArtist)
        private val statusTextView: TextView = itemView.findViewById(R.id.downloadStatus)
        private val qualityBadge: TextView = itemView.findViewById(R.id.downloadQuality)
        private val progressBar: ProgressBar = itemView.findViewById(R.id.downloadProgress)
        private val progressTextView: TextView = itemView.findViewById(R.id.progressText)
        private val sizeTextView: TextView = itemView.findViewById(R.id.downloadSize)
        private val speedTextView: TextView = itemView.findViewById(R.id.downloadSpeed)
        private val etaTextView: TextView = itemView.findViewById(R.id.downloadEta)
        private val actionButton: ImageButton = itemView.findViewById(R.id.actionButton)
        private val deleteButton: ImageButton = itemView.findViewById(R.id.deleteButton)
        private val statusIndicator: View = itemView.findViewById(R.id.statusIndicator)
        
        fun bind(download: Download) {
            // عرض المعلومات الأساسية
            bindBasicInfo(download)
            
            // عرض الحالة والتقدم
            bindStatusAndProgress(download)
            
            // عرض معلومات الحجم والسرعة
            bindSizeAndSpeed(download)
            
            // تحديث الصورة المصغرة
            updateThumbnail(download)
            
            // تحديث أزرار الإجراءات
            updateActionButtons(download)
            
            // إعداد المستمعين
            setupClickListeners(download)
        }
        
        /**
         * ربط المعلومات الأساسية
         */
        private fun bindBasicInfo(download: Download) {
            // العنوان
            titleTextView.text = when {
                download.title.isNotEmpty() && !download.title.contains("جاري") -> download.title
                download.status == DownloadStatus.PENDING -> "جاري استخراج المعلومات..."
                download.status == DownloadStatus.DOWNLOADING -> "جاري التحميل: ${download.title.ifEmpty { "ملف صوتي" }}"
                else -> download.title.ifEmpty { "ملف صوتي" }
            }
            
            // الفنان
            artistTextView.text = when {
                download.artist.isNotEmpty() && !download.artist.contains("جاري") -> download.artist
                download.status == DownloadStatus.PENDING -> "جاري تحديد الفنان..."
                else -> download.artist.ifEmpty { "فنان غير معروف" }
            }
            
            // شارة الجودة والتنسيق
            val durationText = if (download.duration > 0) {
                " • ${formatDuration(download.duration)}"
            } else {
                ""
            }
            qualityBadge.text = "${download.format.uppercase()} • ${download.quality}$durationText"
        }
        
        /**
         * ربط الحالة والتقدم
         */
        private fun bindStatusAndProgress(download: Download) {
            // الحالة
            statusTextView.text = getStatusText(download.status)
            statusTextView.setTextColor(getStatusColor(download.status))
            
            // مؤشر الحالة
            statusIndicator.setBackgroundColor(getStatusColor(download.status))
            
            // شريط التقدم
            val currentProgress = progressMap[download.id] ?: download.getProgressPercentage()
            progressBar.progress = currentProgress
            progressTextView.text = "$currentProgress%"
            
            // إخفاء/إظهار شريط التقدم حسب الحالة
            val showProgress = download.status == DownloadStatus.DOWNLOADING || 
                             download.status == DownloadStatus.PAUSED ||
                             (download.status == DownloadStatus.COMPLETED && currentProgress == 100)
            
            progressBar.visibility = if (showProgress) View.VISIBLE else View.GONE
            progressTextView.visibility = if (showProgress) View.VISIBLE else View.GONE
        }
        
        /**
         * ربط معلومات الحجم والسرعة
         */
        private fun bindSizeAndSpeed(download: Download) {
            // الحجم
            sizeTextView.text = if (download.totalSize > 0) {
                "${download.getFormattedDownloadedSize()} / ${download.getFormattedTotalSize()}"
            } else {
                "حجم غير معروف"
            }
            
            // السرعة
            speedTextView.text = when (download.status) {
                DownloadStatus.DOWNLOADING -> download.speed.ifEmpty { "جاري الحساب..." }
                DownloadStatus.COMPLETED -> "مكتمل"
                DownloadStatus.PAUSED -> "متوقف"
                DownloadStatus.FAILED -> "فاشل"
                else -> "-"
            }
            
            // الوقت المتبقي
            etaTextView.text = when (download.status) {
                DownloadStatus.DOWNLOADING -> download.eta.ifEmpty { "جاري الحساب..." }
                DownloadStatus.COMPLETED -> "✓"
                DownloadStatus.PAUSED -> "⏸"
                DownloadStatus.FAILED -> "✗"
                else -> "-"
            }
        }
        
        /**
         * تحديث الصورة المصغرة
         */
        private fun updateThumbnail(download: Download) {
            val iconResource = when {
                download.status == DownloadStatus.COMPLETED -> R.drawable.ic_music_note
                download.thumbnailUrl.contains("youtube.com") || download.thumbnailUrl.contains("youtu.be") -> {
                    R.drawable.ic_music_library
                }
                download.thumbnailUrl.contains("soundcloud") -> {
                    R.drawable.ic_download
                }
                download.thumbnailUrl.contains("spotify") -> {
                    R.drawable.ic_now_playing
                }
                download.thumbnailUrl.contains("bandcamp") -> {
                    R.drawable.ic_music_library
                }
                download.thumbnailUrl.contains("mixcloud") -> {
                    R.drawable.ic_music_library
                }
                download.status == DownloadStatus.DOWNLOADING -> {
                    R.drawable.ic_download
                }
                download.status == DownloadStatus.FAILED -> {
                    R.drawable.ic_refresh
                }
                else -> {
                    R.drawable.default_album_cover
                }
            }
            
            thumbnailImageView.setImageResource(iconResource)
            
            // تأثير بصري حسب الحالة
            thumbnailImageView.alpha = when (download.status) {
                DownloadStatus.DOWNLOADING -> 0.7f
                DownloadStatus.COMPLETED -> 1.0f
                DownloadStatus.FAILED -> 0.5f
                DownloadStatus.PAUSED -> 0.6f
                else -> 0.8f
            }
        }
        
        /**
         * تحديث أزرار الإجراءات
         */
        private fun updateActionButtons(download: Download) {
            when (download.status) {
                DownloadStatus.DOWNLOADING -> {
                    actionButton.setImageResource(R.drawable.ic_pause)
                    actionButton.visibility = View.VISIBLE
                    actionButton.setOnClickListener { onActionClick(download, "pause") }
                }
                DownloadStatus.PAUSED -> {
                    actionButton.setImageResource(R.drawable.ic_play)
                    actionButton.visibility = View.VISIBLE
                    actionButton.setOnClickListener { onActionClick(download, "resume") }
                }
                DownloadStatus.COMPLETED -> {
                    actionButton.setImageResource(R.drawable.ic_play)
                    actionButton.visibility = View.VISIBLE
                    actionButton.setOnClickListener { onActionClick(download, "play") }
                }
                DownloadStatus.FAILED -> {
                    actionButton.setImageResource(R.drawable.ic_refresh)
                    actionButton.visibility = View.VISIBLE
                    actionButton.setOnClickListener { onActionClick(download, "retry") }
                }
                else -> {
                    actionButton.visibility = View.GONE
                }
            }
        }
        
        /**
         * إعداد مستمعي النقر
         */
        private fun setupClickListeners(download: Download) {
            itemView.setOnClickListener { onDownloadClick(download) }
            deleteButton.setOnClickListener { onDeleteClick(download) }
        }
        
        /**
         * الحصول على نص الحالة
         */
        private fun getStatusText(status: DownloadStatus): String {
            return when (status) {
                DownloadStatus.PENDING -> "في الانتظار"
                DownloadStatus.DOWNLOADING -> "جاري التحميل"
                DownloadStatus.PAUSED -> "متوقف"
                DownloadStatus.COMPLETED -> "مكتمل"
                DownloadStatus.FAILED -> "فاشل"
                DownloadStatus.CANCELLED -> "ملغي"
            }
        }
        
        /**
         * الحصول على لون الحالة
         */
        private fun getStatusColor(status: DownloadStatus): Int {
            return when (status) {
                DownloadStatus.PENDING -> android.graphics.Color.GRAY
                DownloadStatus.DOWNLOADING -> android.graphics.Color.BLUE
                DownloadStatus.PAUSED -> android.graphics.Color.parseColor("#FF9800")
                DownloadStatus.COMPLETED -> android.graphics.Color.GREEN
                DownloadStatus.FAILED -> android.graphics.Color.RED
                DownloadStatus.CANCELLED -> android.graphics.Color.GRAY
            }
        }
        
        /**
         * تنسيق المدة
         */
        private fun formatDuration(seconds: Int): String {
            val minutes = seconds / 60
            val remainingSeconds = seconds % 60
            return if (minutes > 0) {
                "${minutes}:${String.format("%02d", remainingSeconds)}"
            } else {
                "${remainingSeconds}s"
            }
        }
    }
}
