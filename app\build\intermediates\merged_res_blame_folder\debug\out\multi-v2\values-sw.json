{"logs": [{"outputFile": "com.musicplayer.pro.app-mergeDebugResources-59:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\210f3162f1672bd532c02d8af1bd50c1\\transformed\\preference-1.2.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,499,668,753", "endColumns": "69,96,76,149,168,84,82", "endOffsets": "170,267,344,494,663,748,831"}, "to": {"startLines": "65,118,179,181,184,185,186", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5099,9020,14046,14204,14538,14707,14792", "endColumns": "69,96,76,149,168,84,82", "endOffsets": "5164,9112,14118,14349,14702,14787,14870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e081d70774c49b9b7dc6950692deb52\\transformed\\material-1.11.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1047,1139,1212,1275,1361,1423,1486,1551,1619,1682,1736,1868,1925,1987,2041,2115,2253,2334,2414,2546,2631,2718,2859,2947,3026,3080,3133,3199,3271,3353,3443,3528,3600,3675,3746,3819,3925,4022,4096,4191,4288,4362,4447,4547,4600,4685,4753,4841,4931,4993,5057,5120,5187,5304,5416,5527,5638,5696,5753", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,140,87,78,53,52,65,71,81,89,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80", "endOffsets": "254,330,404,477,574,663,762,891,974,1042,1134,1207,1270,1356,1418,1481,1546,1614,1677,1731,1863,1920,1982,2036,2110,2248,2329,2409,2541,2626,2713,2854,2942,3021,3075,3128,3194,3266,3348,3438,3523,3595,3670,3741,3814,3920,4017,4091,4186,4283,4357,4442,4542,4595,4680,4748,4836,4926,4988,5052,5115,5182,5299,5411,5522,5633,5691,5748,5829"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,66,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "801,3656,3732,3806,3879,3976,4788,4887,5016,5169,9117,9209,9282,9345,9431,9493,9556,9621,9689,9752,9806,9938,9995,10057,10111,10185,10323,10404,10484,10616,10701,10788,10929,11017,11096,11150,11203,11269,11341,11423,11513,11598,11670,11745,11816,11889,11995,12092,12166,12261,12358,12432,12517,12617,12670,12755,12823,12911,13001,13063,13127,13190,13257,13374,13486,13597,13708,13766,14123", "endLines": "22,50,51,52,53,54,62,63,64,66,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,180", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,140,87,78,53,52,65,71,81,89,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80", "endOffsets": "955,3727,3801,3874,3971,4060,4882,5011,5094,5232,9204,9277,9340,9426,9488,9551,9616,9684,9747,9801,9933,9990,10052,10106,10180,10318,10399,10479,10611,10696,10783,10924,11012,11091,11145,11198,11264,11336,11418,11508,11593,11665,11740,11811,11884,11990,12087,12161,12256,12353,12427,12512,12612,12665,12750,12818,12906,12996,13058,13122,13185,13252,13369,13481,13592,13703,13761,13818,14199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7c108dd56627f30fe94755d1a2faeaf2\\transformed\\core-1.12.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "55,56,57,58,59,60,61,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4065,4159,4261,4358,4459,4566,4673,14437", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "4154,4256,4353,4454,4561,4668,4783,14533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f0c4d03d60aaed2968a93a146657af49\\transformed\\jetified-media3-exoplayer-1.2.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,177,239,306,384,465,552,634", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "113,172,234,301,379,460,547,629,699"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7228,7291,7350,7412,7479,7557,7638,7725,7807", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "7286,7345,7407,7474,7552,7633,7720,7802,7872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\472040ed58573b5e54239e60b254baef\\transformed\\jetified-media3-ui-1.2.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,289,506,706,785,862,947,1037,1125,1201,1267,1360,1455,1522,1586,1647,1722,1835,1952,2065,2139,2220,2293,2371,2462,2551,2619,2697,2750,2808,2856,2917,2978,3045,3109,3175,3238,3297,3363,3432,3498,3550,3616,3699,3782", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,112,116,112,73,80,72,77,90,88,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "284,501,701,780,857,942,1032,1120,1196,1262,1355,1450,1517,1581,1642,1717,1830,1947,2060,2134,2215,2288,2366,2457,2546,2614,2692,2745,2803,2851,2912,2973,3040,3104,3170,3233,3292,3358,3427,3493,3545,3611,3694,3777,3835"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,384,601,5237,5316,5393,5478,5568,5656,5732,5798,5891,5986,6053,6117,6178,6253,6366,6483,6596,6670,6751,6824,6902,6993,7082,7150,7877,7930,7988,8036,8097,8158,8225,8289,8355,8418,8477,8543,8612,8678,8730,8796,8879,8962", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,112,116,112,73,80,72,77,90,88,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "379,596,796,5311,5388,5473,5563,5651,5727,5793,5886,5981,6048,6112,6173,6248,6361,6478,6591,6665,6746,6819,6897,6988,7077,7145,7223,7925,7983,8031,8092,8153,8220,8284,8350,8413,8472,8538,8607,8673,8725,8791,8874,8957,9015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d0962e3cd95d785309666b77b5473090\\transformed\\appcompat-1.6.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "960,1063,1162,1270,1360,1465,1582,1665,1747,1838,1931,2026,2120,2220,2313,2408,2502,2593,2684,2766,2867,2975,3074,3181,3293,3397,3559,14354", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "1058,1157,1265,1355,1460,1577,1660,1742,1833,1926,2021,2115,2215,2308,2403,2497,2588,2679,2761,2862,2970,3069,3176,3288,3392,3554,3651,14432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e913642d7c50f47db3e63580f9572497\\transformed\\navigation-ui-2.7.6\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,117", "endOffsets": "155,273"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "13823,13928", "endColumns": "104,117", "endOffsets": "13923,14041"}}]}]}