{"logs": [{"outputFile": "com.musicplayer.pro.app-mergeDebugResources-59:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d0962e3cd95d785309666b77b5473090\\transformed\\appcompat-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1303,1419,1523,1636,1723,1825,1947,2030,2110,2204,2300,2397,2493,2596,2692,2790,2886,2980,3074,3157,3266,3374,3474,3584,3689,3795,3971,14886", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "1414,1518,1631,1718,1820,1942,2025,2105,2199,2295,2392,2488,2591,2687,2785,2881,2975,3069,3152,3261,3369,3469,3579,3684,3790,3966,4067,14965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e081d70774c49b9b7dc6950692deb52\\transformed\\material-1.11.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1163,1262,1340,1405,1515,1578,1650,1709,1783,1844,1898,2022,2083,2145,2199,2277,2411,2499,2583,2724,2803,2887,3030,3127,3204,3260,3314,3380,3455,3534,3622,3702,3778,3856,3929,4006,4113,4200,4281,4371,4463,4535,4616,4708,4763,4845,4911,4996,5083,5145,5209,5272,5344,5455,5571,5672,5781,5841,5899", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,142,96,76,55,53,65,74,78,87,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1158,1257,1335,1400,1510,1573,1645,1704,1778,1839,1893,2017,2078,2140,2194,2272,2406,2494,2578,2719,2798,2882,3025,3122,3199,3255,3309,3375,3450,3529,3617,3697,3773,3851,3924,4001,4108,4195,4276,4366,4458,4530,4611,4703,4758,4840,4906,4991,5078,5140,5204,5267,5339,5450,5566,5667,5776,5836,5894,5976"}, "to": {"startLines": "23,56,57,58,59,60,68,69,70,72,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1028,4072,4151,4229,4312,4406,5247,5343,5461,5618,9615,9714,9792,9857,9967,10030,10102,10161,10235,10296,10350,10474,10535,10597,10651,10729,10863,10951,11035,11176,11255,11339,11482,11579,11656,11712,11766,11832,11907,11986,12074,12154,12230,12308,12381,12458,12565,12652,12733,12823,12915,12987,13068,13160,13215,13297,13363,13448,13535,13597,13661,13724,13796,13907,14023,14124,14233,14293,14658", "endLines": "28,56,57,58,59,60,68,69,70,72,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,186", "endColumns": "12,78,77,82,93,89,95,117,83,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,83,140,78,83,142,96,76,55,53,65,74,78,87,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81", "endOffsets": "1298,4146,4224,4307,4401,4491,5338,5456,5540,5679,9709,9787,9852,9962,10025,10097,10156,10230,10291,10345,10469,10530,10592,10646,10724,10858,10946,11030,11171,11250,11334,11477,11574,11651,11707,11761,11827,11902,11981,12069,12149,12225,12303,12376,12453,12560,12647,12728,12818,12910,12982,13063,13155,13210,13292,13358,13443,13530,13592,13656,13719,13791,13902,14018,14119,14228,14288,14346,14735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\210f3162f1672bd532c02d8af1bd50c1\\transformed\\preference-1.2.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,269,347,493,662,747", "endColumns": "72,90,77,145,168,84,80", "endOffsets": "173,264,342,488,657,742,823"}, "to": {"startLines": "71,124,185,187,190,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5545,9524,14580,14740,15071,15240,15325", "endColumns": "72,90,77,145,168,84,80", "endOffsets": "5613,9610,14653,14881,15235,15320,15401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f0c4d03d60aaed2968a93a146657af49\\transformed\\jetified-media3-exoplayer-1.2.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,200,267,335,416,490,587,682", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "131,195,262,330,411,485,582,677,752"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7659,7740,7804,7871,7939,8020,8094,8191,8286", "endColumns": "80,63,66,67,80,73,96,94,74", "endOffsets": "7735,7799,7866,7934,8015,8089,8186,8281,8356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\472040ed58573b5e54239e60b254baef\\transformed\\jetified-media3-ui-1.2.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,298,622,933,1017,1100,1178,1275,1372,1446,1510,1606,1702,1773,1838,1901,1974,2082,2192,2300,2372,2448,2521,2595,2684,2772,2841,2908,2961,3019,3074,3135,3201,3270,3335,3403,3467,3525,3598,3665,3739,3798,3861,3938,4015", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,107,109,107,71,75,72,73,88,87,68,66,52,57,54,60,65,68,64,67,63,57,72,66,73,58,62,76,76,55", "endOffsets": "293,617,928,1012,1095,1173,1270,1367,1441,1505,1601,1697,1768,1833,1896,1969,2077,2187,2295,2367,2443,2516,2590,2679,2767,2836,2903,2956,3014,3069,3130,3196,3265,3330,3398,3462,3520,3593,3660,3734,3793,3856,3933,4010,4066"}, "to": {"startLines": "2,11,17,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,393,717,5684,5768,5851,5929,6026,6123,6197,6261,6357,6453,6524,6589,6652,6725,6833,6943,7051,7123,7199,7272,7346,7435,7523,7592,8361,8414,8472,8527,8588,8654,8723,8788,8856,8920,8978,9051,9118,9192,9251,9314,9391,9468", "endLines": "10,16,22,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "endColumns": "17,12,12,83,82,77,96,96,73,63,95,95,70,64,62,72,107,109,107,71,75,72,73,88,87,68,66,52,57,54,60,65,68,64,67,63,57,72,66,73,58,62,76,76,55", "endOffsets": "388,712,1023,5763,5846,5924,6021,6118,6192,6256,6352,6448,6519,6584,6647,6720,6828,6938,7046,7118,7194,7267,7341,7430,7518,7587,7654,8409,8467,8522,8583,8649,8718,8783,8851,8915,8973,9046,9113,9187,9246,9309,9386,9463,9519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e913642d7c50f47db3e63580f9572497\\transformed\\navigation-ui-2.7.6\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "183,184", "startColumns": "4,4", "startOffsets": "14351,14461", "endColumns": "109,118", "endOffsets": "14456,14575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7c108dd56627f30fe94755d1a2faeaf2\\transformed\\core-1.12.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "61,62,63,64,65,66,67,189", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4496,4594,4704,4803,4906,5017,5127,14970", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "4589,4699,4798,4901,5012,5122,5242,15066"}}]}]}