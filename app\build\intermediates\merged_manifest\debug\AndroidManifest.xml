<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.musicplayer.pro"
    android:versionCode="1"
    android:versionName="1.0.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="34" />

    <!-- الأذونات الأساسية -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- أذونات التخزين -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <!-- أذونات الصوت -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- أذونات الخدمات -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- أذونات البلوتوث -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

    <!-- أذونات الإشعارات -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />

    <!-- أذونات إضافية (اختيارية) -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />

    <!-- دعم الميزات -->
    <uses-feature
        android:name="android.hardware.audio.output"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.microphone"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false" />

    <!-- دعم Android Auto -->
    <uses-feature
        android:name="android.hardware.type.automotive"
        android:required="false" />

    <!-- دعم Android TV -->
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />

    <!-- دعم Wear OS -->
    <uses-feature
        android:name="android.hardware.type.watch"
        android:required="false" />

    <permission
        android:name="com.musicplayer.pro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.musicplayer.pro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.musicplayer.pro.MusicPlayerApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:preserveLegacyExternalStorage="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.MusicPlayer.Modern" >

        <!-- النشاط الرئيسي -->
        <activity
            android:name="com.musicplayer.pro.MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.MusicPlayerPro" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- دعم ملفات الصوت -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:mimeType="audio/*" />
            </intent-filter>

            <!-- دعم الروابط -->
            <intent-filter android:autoVerify="true" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="youtube.com"
                    android:scheme="http" />
                <data
                    android:host="youtube.com"
                    android:scheme="https" />
                <data
                    android:host="www.youtube.com"
                    android:scheme="http" />
                <data
                    android:host="www.youtube.com"
                    android:scheme="https" />
                <data
                    android:host="youtu.be"
                    android:scheme="http" />
                <data
                    android:host="youtu.be"
                    android:scheme="https" />
            </intent-filter>
        </activity>

        <!-- نشاط الإعدادات -->
        <activity
            android:name="com.musicplayer.pro.SettingsActivity"
            android:exported="false"
            android:parentActivityName="com.musicplayer.pro.MainActivity"
            android:theme="@style/Theme.MusicPlayerPro" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".MainActivity" />
        </activity>

        <!-- نشاط المعادل الصوتي -->
        <activity
            android:name="com.musicplayer.pro.EqualizerActivity"
            android:exported="false"
            android:parentActivityName="com.musicplayer.pro.SettingsActivity"
            android:theme="@style/Theme.MusicPlayerPro" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".SettingsActivity" />
        </activity>

        <!-- خدمة الموسيقى -->
        <service
            android:name="com.musicplayer.pro.services.MusicService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback" >
            <intent-filter>
                <action android:name="android.media.browse.MediaBrowserService" />
            </intent-filter>
        </service>

        <!-- خدمة التحميل -->
        <service
            android:name="com.musicplayer.pro.services.DownloadService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- مستقبل البث للتحكم في الوسائط -->
        <receiver
            android:name="com.musicplayer.pro.receivers.MediaButtonReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>

        <!-- مستقبل البث لبدء التشغيل -->
        <receiver
            android:name="com.musicplayer.pro.receivers.BootReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- مستقبل البث للسماعات -->
        <receiver
            android:name="com.musicplayer.pro.receivers.HeadsetReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.HEADSET_PLUG" />
                <action android:name="android.bluetooth.a2dp.profile.action.CONNECTION_STATE_CHANGED" />
            </intent-filter>
        </receiver>

        <!-- مزود المحتوى للملفات -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.musicplayer.pro.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- MediaSession -->
        <meta-data
            android:name="android.media.session.MediaSession"
            android:value="true" />

        <!-- دعم Auto -->
        <meta-data
            android:name="com.google.android.gms.car.application"
            android:resource="@xml/automotive_app_desc" />

        <!-- دعم Wear OS -->
        <meta-data
            android:name="com.google.android.wearable.standalone"
            android:value="false" />

        <!-- إعدادات الشبكة -->
        <meta-data
            android:name="android.webkit.WebView.MetricsOptOut"
            android:value="true" />

        <activity
            android:name="com.karumi.dexter.DexterActivity"
            android:theme="@style/Dexter.Internal.Theme.Transparent" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.musicplayer.pro.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>