// Generated by view binder compiler. Do not edit!
package com.musicplayer.pro.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import com.musicplayer.pro.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentAdvancedDownloadBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton downloadButton;

  @NonNull
  public final RecyclerView downloadsRecyclerView;

  @NonNull
  public final FloatingActionButton fab;

  @NonNull
  public final Spinner formatSpinner;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Spinner qualitySpinner;

  @NonNull
  public final TextView statsTextView;

  @NonNull
  public final TextView statusTextView;

  @NonNull
  public final TextInputEditText urlEditText;

  private FragmentAdvancedDownloadBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton downloadButton, @NonNull RecyclerView downloadsRecyclerView,
      @NonNull FloatingActionButton fab, @NonNull Spinner formatSpinner,
      @NonNull ProgressBar progressBar, @NonNull Spinner qualitySpinner,
      @NonNull TextView statsTextView, @NonNull TextView statusTextView,
      @NonNull TextInputEditText urlEditText) {
    this.rootView = rootView;
    this.downloadButton = downloadButton;
    this.downloadsRecyclerView = downloadsRecyclerView;
    this.fab = fab;
    this.formatSpinner = formatSpinner;
    this.progressBar = progressBar;
    this.qualitySpinner = qualitySpinner;
    this.statsTextView = statsTextView;
    this.statusTextView = statusTextView;
    this.urlEditText = urlEditText;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentAdvancedDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentAdvancedDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_advanced_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentAdvancedDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.downloadButton;
      MaterialButton downloadButton = ViewBindings.findChildViewById(rootView, id);
      if (downloadButton == null) {
        break missingId;
      }

      id = R.id.downloadsRecyclerView;
      RecyclerView downloadsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (downloadsRecyclerView == null) {
        break missingId;
      }

      id = R.id.fab;
      FloatingActionButton fab = ViewBindings.findChildViewById(rootView, id);
      if (fab == null) {
        break missingId;
      }

      id = R.id.formatSpinner;
      Spinner formatSpinner = ViewBindings.findChildViewById(rootView, id);
      if (formatSpinner == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.qualitySpinner;
      Spinner qualitySpinner = ViewBindings.findChildViewById(rootView, id);
      if (qualitySpinner == null) {
        break missingId;
      }

      id = R.id.statsTextView;
      TextView statsTextView = ViewBindings.findChildViewById(rootView, id);
      if (statsTextView == null) {
        break missingId;
      }

      id = R.id.statusTextView;
      TextView statusTextView = ViewBindings.findChildViewById(rootView, id);
      if (statusTextView == null) {
        break missingId;
      }

      id = R.id.urlEditText;
      TextInputEditText urlEditText = ViewBindings.findChildViewById(rootView, id);
      if (urlEditText == null) {
        break missingId;
      }

      return new FragmentAdvancedDownloadBinding((CoordinatorLayout) rootView, downloadButton,
          downloadsRecyclerView, fab, formatSpinner, progressBar, qualitySpinner, statsTextView,
          statusTextView, urlEditText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
