<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary">

    <!-- شريط الأدوات -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/surface_primary"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/equalizer"
        app:titleTextColor="@color/text_primary" />

    <!-- أدوات التحكم في التأثيرات -->
    <LinearLayout
        android:id="@+id/effectsControlsLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/surface_secondary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <!-- Bass Boost -->
        <TextView
            android:id="@+id/bassBoostLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Bass Boost: 0%"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <SeekBar
            android:id="@+id/bassBoostSeekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:max="100"
            android:progress="0"
            android:progressTint="@color/accent_primary"
            android:thumbTint="@color/accent_primary" />

        <!-- Virtualizer -->
        <TextView
            android:id="@+id/virtualizerLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Virtualizer: 0%"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <SeekBar
            android:id="@+id/virtualizerSeekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:max="100"
            android:progress="0"
            android:progressTint="@color/accent_primary"
            android:thumbTint="@color/accent_primary" />

    </LinearLayout>

    <!-- عنوان المعادل -->
    <TextView
        android:id="@+id/equalizerTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:padding="16dp"
        android:text="@string/equalizer_bands"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/effectsControlsLayout" />

    <!-- قائمة نطاقات المعادل -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewEqualizer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/equalizerTitle"
        tools:listitem="@layout/item_equalizer_band" />

</androidx.constraintlayout.widget.ConstraintLayout>
