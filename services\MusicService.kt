package com.musicplayer.pro.services

import android.app.*
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import android.support.v4.media.session.MediaSessionCompat
import android.support.v4.media.session.PlaybackStateCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.musicplayer.pro.R
import com.musicplayer.pro.models.Song
import com.musicplayer.pro.models.RepeatMode
import com.musicplayer.pro.utils.AudioFocusHelper
import com.musicplayer.pro.utils.MediaSessionHelper
import com.musicplayer.pro.utils.NotificationHelper
import kotlinx.coroutines.*
import java.io.IOException

/**
 * خدمة تشغيل الموسيقى
 * تدير تشغيل الصوت في الخلفية وإشعارات التحكم
 */
class MusicService : Service(), MediaPlayer.OnPreparedListener, 
    MediaPlayer.OnCompletionListener, MediaPlayer.OnErrorListener {
    
    companion object {
        private const val TAG = "MusicService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "music_playback_channel"
        
        // Actions للإشعارات
        const val ACTION_PLAY_PAUSE = "com.musicplayer.pro.PLAY_PAUSE"
        const val ACTION_NEXT = "com.musicplayer.pro.NEXT"
        const val ACTION_PREVIOUS = "com.musicplayer.pro.PREVIOUS"
        const val ACTION_STOP = "com.musicplayer.pro.STOP"
    }
    
    // Binder للتواصل مع Activity
    private val binder = MusicBinder()
    
    // MediaPlayer
    private var mediaPlayer: MediaPlayer? = null
    private var wakeLock: PowerManager.WakeLock? = null
    
    // Current state
    private var currentSong: Song? = null
    private var playlist: MutableList<Song> = mutableListOf()
    private var currentIndex: Int = -1
    private var isShuffleEnabled: Boolean = false
    private var repeatMode: RepeatMode = RepeatMode.OFF
    private var isPlaying: Boolean = false
    private var isPrepared: Boolean = false
    
    // Helpers
    private lateinit var audioFocusHelper: AudioFocusHelper
    private lateinit var mediaSessionHelper: MediaSessionHelper
    private lateinit var notificationHelper: NotificationHelper
    
    // Coroutines
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // Listeners
    private var onPlaybackStateChangedListener: ((Boolean) -> Unit)? = null
    private var onSongChangedListener: ((Song?) -> Unit)? = null
    private var onProgressChangedListener: ((Int, Int) -> Unit)? = null

    // مستمع تشغيل الأغاني المحملة
    private val playSongReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.musicplayer.pro.PLAY_SONG") {
                val song = intent.getParcelableExtra<Song>("song")
                song?.let {
                    android.util.Log.d(TAG, "تم استلام طلب تشغيل أغنية: ${it.title}")
                    playSong(it, listOf(it))
                }
            }
        }
    }
    
    inner class MusicBinder : Binder() {
        fun getService(): MusicService = this@MusicService
    }
    
    override fun onCreate() {
        super.onCreate()
        
        // تهيئة المساعدين
        audioFocusHelper = AudioFocusHelper(this)
        mediaSessionHelper = MediaSessionHelper(this)
        notificationHelper = NotificationHelper(this)
        
        // إنشاء قناة الإشعارات
        createNotificationChannel()
        
        // إعداد MediaSession
        setupMediaSession()
        
        // إعداد WakeLock
        setupWakeLock()
        
        // بدء مراقبة التقدم
        startProgressTracking()

        // تسجيل مستمع تشغيل الأغاني
        registerPlaySongReceiver()
    }
    
    override fun onBind(intent: Intent?): IBinder = binder
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        handleIntent(intent)
        return START_STICKY
    }
    
    /**
     * معالجة الأوامر من الإشعارات
     */
    private fun handleIntent(intent: Intent?) {
        when (intent?.action) {
            ACTION_PLAY_PAUSE -> togglePlayPause()
            ACTION_NEXT -> playNext()
            ACTION_PREVIOUS -> playPrevious()
            ACTION_STOP -> stopPlayback()
        }
    }
    
    /**
     * إنشاء قناة الإشعارات
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                getString(R.string.music_playback_channel),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.music_playback_channel_description)
                setShowBadge(false)
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * إعداد MediaSession
     */
    private fun setupMediaSession() {
        mediaSessionHelper.setupCallbacks(
            onPlay = { resumePlayback() },
            onPause = { pausePlayback() },
            onNext = { playNext() },
            onPrevious = { playPrevious() },
            onStop = { stopPlayback() },
            onSeekTo = { position -> seekTo(position.toInt()) }
        )
    }
    
    /**
     * إعداد WakeLock
     */
    private fun setupWakeLock() {
        val powerManager = getSystemService(POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "MusicPlayer::WakeLock"
        )
    }
    
    /**
     * تشغيل أغنية
     */
    fun playSong(song: Song, playlist: List<Song> = listOf(song)) {
        this.playlist.clear()
        this.playlist.addAll(playlist)
        this.currentIndex = playlist.indexOf(song)
        this.currentSong = song
        
        prepareAndPlay(song)
    }
    
    /**
     * تحضير وتشغيل الأغنية
     */
    private fun prepareAndPlay(song: Song) {
        try {
            // إيقاف التشغيل الحالي
            stopCurrentPlayback()
            
            // إنشاء MediaPlayer جديد
            mediaPlayer = MediaPlayer().apply {
                setAudioAttributes(
                    AudioAttributes.Builder()
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .build()
                )
                
                setDataSource(song.path)
                setOnPreparedListener(this@MusicService)
                setOnCompletionListener(this@MusicService)
                setOnErrorListener(this@MusicService)
                
                prepareAsync()
            }
            
            isPrepared = false
            
            // طلب Audio Focus
            audioFocusHelper.requestAudioFocus { granted ->
                if (granted) {
                    // تحديث الإشعار
                    updateNotification()
                } else {
                    pausePlayback()
                }
            }
            
        } catch (e: IOException) {
            e.printStackTrace()
            onError(mediaPlayer, MediaPlayer.MEDIA_ERROR_UNKNOWN, 0)
        }
    }
    
    /**
     * تبديل التشغيل/الإيقاف
     */
    fun togglePlayPause() {
        if (isPlaying) {
            pausePlayback()
        } else {
            resumePlayback()
        }
    }
    
    /**
     * إيقاف التشغيل مؤقتاً
     */
    fun pausePlayback() {
        mediaPlayer?.pause()
        isPlaying = false
        
        // تحرير WakeLock
        wakeLock?.let { if (it.isHeld) it.release() }
        
        // تحديث MediaSession
        mediaSessionHelper.updatePlaybackState(PlaybackStateCompat.STATE_PAUSED)
        
        // تحديث الإشعار
        updateNotification()
        
        // إشعار المستمعين
        onPlaybackStateChangedListener?.invoke(false)
    }
    
    /**
     * استئناف التشغيل
     */
    fun resumePlayback() {
        if (isPrepared) {
            mediaPlayer?.start()
            isPlaying = true
            
            // الحصول على WakeLock
            wakeLock?.acquire(10*60*1000L /*10 minutes*/)
            
            // تحديث MediaSession
            mediaSessionHelper.updatePlaybackState(PlaybackStateCompat.STATE_PLAYING)
            
            // تحديث الإشعار
            updateNotification()
            
            // إشعار المستمعين
            onPlaybackStateChangedListener?.invoke(true)
        }
    }
    
    /**
     * تشغيل الأغنية التالية
     */
    fun playNext() {
        if (playlist.isEmpty()) return
        
        when {
            isShuffleEnabled -> {
                // تشغيل عشوائي
                val randomIndex = (0 until playlist.size).random()
                currentIndex = randomIndex
            }
            currentIndex < playlist.size - 1 -> {
                currentIndex++
            }
            repeatMode == RepeatMode.ALL -> {
                currentIndex = 0
            }
            else -> {
                // نهاية القائمة
                stopPlayback()
                return
            }
        }
        
        val nextSong = playlist[currentIndex]
        currentSong = nextSong
        prepareAndPlay(nextSong)
        
        // إشعار المستمعين
        onSongChangedListener?.invoke(nextSong)
    }
    
    /**
     * تشغيل الأغنية السابقة
     */
    fun playPrevious() {
        if (playlist.isEmpty()) return
        
        when {
            currentIndex > 0 -> {
                currentIndex--
            }
            repeatMode == RepeatMode.ALL -> {
                currentIndex = playlist.size - 1
            }
            else -> {
                // بداية القائمة
                seekTo(0)
                return
            }
        }
        
        val previousSong = playlist[currentIndex]
        currentSong = previousSong
        prepareAndPlay(previousSong)
        
        // إشعار المستمعين
        onSongChangedListener?.invoke(previousSong)
    }
    
    /**
     * إيقاف التشغيل
     */
    fun stopPlayback() {
        stopCurrentPlayback()
        
        // إيقاف الخدمة
        stopForeground(true)
        stopSelf()
    }
    
    /**
     * إيقاف التشغيل الحالي
     */
    private fun stopCurrentPlayback() {
        mediaPlayer?.apply {
            if (isPlaying) stop()
            release()
        }
        mediaPlayer = null
        isPlaying = false
        isPrepared = false
        
        // تحرير WakeLock
        wakeLock?.let { if (it.isHeld) it.release() }
        
        // تحديث MediaSession
        mediaSessionHelper.updatePlaybackState(PlaybackStateCompat.STATE_STOPPED)
    }
    
    /**
     * الانتقال لموضع معين
     */
    fun seekTo(position: Int) {
        mediaPlayer?.seekTo(position)
        mediaSessionHelper.updatePlaybackState(
            if (isPlaying) PlaybackStateCompat.STATE_PLAYING else PlaybackStateCompat.STATE_PAUSED,
            position.toLong()
        )
    }
    
    /**
     * الحصول على الموضع الحالي
     */
    fun getCurrentPosition(): Int {
        return mediaPlayer?.currentPosition ?: 0
    }
    
    /**
     * الحصول على مدة الأغنية
     */
    fun getDuration(): Int {
        return mediaPlayer?.duration ?: 0
    }
    
    // MediaPlayer Callbacks
    override fun onPrepared(mp: MediaPlayer?) {
        isPrepared = true
        resumePlayback()
    }
    
    override fun onCompletion(mp: MediaPlayer?) {
        when (repeatMode) {
            RepeatMode.ONE -> {
                // إعادة تشغيل نفس الأغنية
                seekTo(0)
                resumePlayback()
            }
            else -> {
                // تشغيل الأغنية التالية
                playNext()
            }
        }
    }
    
    override fun onError(mp: MediaPlayer?, what: Int, extra: Int): Boolean {
        // معالجة الأخطاء
        stopCurrentPlayback()
        return true
    }
    
    /**
     * تحديث الإشعار
     */
    private fun updateNotification() {
        val notification = notificationHelper.createNotification(
            currentSong,
            isPlaying
        )
        
        startForeground(NOTIFICATION_ID, notification)
    }
    
    /**
     * بدء مراقبة التقدم
     */
    private fun startProgressTracking() {
        serviceScope.launch {
            while (true) {
                if (isPlaying && isPrepared) {
                    val current = getCurrentPosition()
                    val duration = getDuration()
                    onProgressChangedListener?.invoke(current, duration)
                }
                delay(1000) // تحديث كل ثانية
            }
        }
    }
    
    // Setters للمستمعين
    fun setOnPlaybackStateChangedListener(listener: (Boolean) -> Unit) {
        onPlaybackStateChangedListener = listener
    }
    
    fun setOnSongChangedListener(listener: (Song?) -> Unit) {
        onSongChangedListener = listener
    }
    
    fun setOnProgressChangedListener(listener: (Int, Int) -> Unit) {
        onProgressChangedListener = listener
    }
    
    // Getters
    fun getCurrentSong(): Song? = currentSong
    fun isPlaying(): Boolean = isPlaying
    fun getPlaylist(): List<Song> = playlist.toList()
    fun getCurrentIndex(): Int = currentIndex
    fun isShuffleEnabled(): Boolean = isShuffleEnabled
    fun getRepeatMode(): RepeatMode = repeatMode
    
    // Setters
    fun setShuffleEnabled(enabled: Boolean) {
        isShuffleEnabled = enabled
    }
    
    fun setRepeatMode(mode: RepeatMode) {
        repeatMode = mode
    }
    
    /**
     * تسجيل مستمع تشغيل الأغاني
     */
    private fun registerPlaySongReceiver() {
        try {
            val filter = IntentFilter("com.musicplayer.pro.PLAY_SONG")
            registerReceiver(playSongReceiver, filter)
            android.util.Log.d(TAG, "تم تسجيل مستمع تشغيل الأغاني")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "خطأ في تسجيل مستمع تشغيل الأغاني: ${e.message}")
        }
    }

    /**
     * إلغاء تسجيل مستمع تشغيل الأغاني
     */
    private fun unregisterPlaySongReceiver() {
        try {
            unregisterReceiver(playSongReceiver)
            android.util.Log.d(TAG, "تم إلغاء تسجيل مستمع تشغيل الأغاني")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "خطأ في إلغاء تسجيل مستمع تشغيل الأغاني: ${e.message}")
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // إلغاء تسجيل المستمع
        unregisterPlaySongReceiver()

        // تنظيف الموارد
        stopCurrentPlayback()
        audioFocusHelper.abandonAudioFocus()
        mediaSessionHelper.release()
        serviceScope.cancel()

        wakeLock?.let {
            if (it.isHeld) it.release()
        }
    }
}
