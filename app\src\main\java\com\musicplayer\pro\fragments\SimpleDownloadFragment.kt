package com.musicplayer.pro.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.musicplayer.pro.R
import com.musicplayer.pro.download.SimpleDownloadSystem
import com.musicplayer.pro.download.SimpleDownload
import com.musicplayer.pro.download.SimpleDownloadStatus
import kotlinx.coroutines.launch

/**
 * Fragment بسيط للتحميل - حل جذري
 */
class SimpleDownloadFragment : Fragment() {
    
    private lateinit var downloadSystem: SimpleDownloadSystem
    private lateinit var adapter: SimpleDownloadAdapter
    
    // Views
    private lateinit var urlEditText: EditText
    private lateinit var downloadButton: Button
    private lateinit var recyclerView: RecyclerView
    private lateinit var statusText: TextView
    private lateinit var progressBar: ProgressBar
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_simple_download, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initViews(view)
        setupDownloadSystem()
        setupRecyclerView()
        setupClickListeners()
        loadDownloads()
    }
    
    /**
     * تهيئة Views
     */
    private fun initViews(view: View) {
        urlEditText = view.findViewById(R.id.urlEditText)
        downloadButton = view.findViewById(R.id.downloadButton)
        recyclerView = view.findViewById(R.id.downloadsRecyclerView)
        statusText = view.findViewById(R.id.statusText)
        progressBar = view.findViewById(R.id.progressBar)
    }
    
    /**
     * إعداد نظام التحميل
     */
    private fun setupDownloadSystem() {
        downloadSystem = SimpleDownloadSystem.getInstance(requireContext())
        
        // إعداد المستمعين
        downloadSystem.setOnProgressListener { downloadId, progress ->
            activity?.runOnUiThread {
                adapter.updateProgress(downloadId, progress)
                statusText.text = "جاري التحميل... $progress%"
            }
        }
        
        downloadSystem.setOnCompleteListener { download ->
            activity?.runOnUiThread {
                adapter.notifyDataSetChanged()
                statusText.text = "تم اكتمال التحميل: ${download.title}"
                showToast("تم اكتمال التحميل: ${download.title}")
                loadDownloads()
            }
        }
        
        downloadSystem.setOnErrorListener { downloadId, error ->
            activity?.runOnUiThread {
                statusText.text = "خطأ في التحميل: $error"
                showToast("خطأ في التحميل: $error")
                loadDownloads()
            }
        }
    }
    
    /**
     * إعداد RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = SimpleDownloadAdapter { download ->
            handleDownloadClick(download)
        }
        
        recyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = <EMAIL>
        }
    }
    
    /**
     * إعداد مستمعي النقر
     */
    private fun setupClickListeners() {
        downloadButton.setOnClickListener {
            startDownload()
        }
    }
    
    /**
     * بدء التحميل
     */
    private fun startDownload() {
        val url = urlEditText.text.toString().trim()
        
        if (url.isEmpty()) {
            showToast("يرجى إدخال رابط صحيح")
            return
        }
        
        if (!isValidUrl(url)) {
            showToast("رابط غير صحيح")
            return
        }
        
        lifecycleScope.launch {
            try {
                progressBar.visibility = View.VISIBLE
                downloadButton.isEnabled = false
                statusText.text = "جاري بدء التحميل..."
                
                val downloadId = downloadSystem.startDownload(url)
                
                statusText.text = "تم بدء التحميل بنجاح"
                urlEditText.text.clear()
                loadDownloads()
                
                android.util.Log.d("SimpleDownloadFragment", "تم بدء التحميل: $downloadId")
                
            } catch (e: Exception) {
                statusText.text = "خطأ في بدء التحميل: ${e.message}"
                showToast("خطأ في بدء التحميل: ${e.message}")
                android.util.Log.e("SimpleDownloadFragment", "خطأ في بدء التحميل", e)
            } finally {
                progressBar.visibility = View.GONE
                downloadButton.isEnabled = true
            }
        }
    }
    
    /**
     * التعامل مع نقر التحميل
     */
    private fun handleDownloadClick(download: SimpleDownload) {
        when (download.status) {
            SimpleDownloadStatus.COMPLETED -> {
                // تشغيل الملف
                downloadSystem.playDownload(download.id)
                showToast("جاري تشغيل: ${download.title}")
            }
            SimpleDownloadStatus.DOWNLOADING -> {
                showToast("التحميل جاري... ${download.progress}%")
            }
            SimpleDownloadStatus.FAILED -> {
                showToast("فشل التحميل: ${download.title}")
            }
        }
    }
    
    /**
     * تحميل قائمة التحميلات
     */
    private fun loadDownloads() {
        try {
            val downloads = downloadSystem.getAllDownloads()
            adapter.updateDownloads(downloads)
            
            if (downloads.isEmpty()) {
                statusText.text = "لا توجد تحميلات"
            } else {
                val completed = downloads.count { it.status == SimpleDownloadStatus.COMPLETED }
                val downloading = downloads.count { it.status == SimpleDownloadStatus.DOWNLOADING }
                val failed = downloads.count { it.status == SimpleDownloadStatus.FAILED }
                
                statusText.text = "المجموع: ${downloads.size} | مكتمل: $completed | جاري: $downloading | فاشل: $failed"
            }
            
        } catch (e: Exception) {
            android.util.Log.e("SimpleDownloadFragment", "خطأ في تحميل التحميلات: ${e.message}")
        }
    }
    
    /**
     * التحقق من صحة الرابط
     */
    private fun isValidUrl(url: String): Boolean {
        return try {
            url.startsWith("http://") || url.startsWith("https://")
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * عرض Toast
     */
    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }
    
    override fun onResume() {
        super.onResume()
        loadDownloads()
    }
}

/**
 * Adapter بسيط للتحميلات
 */
class SimpleDownloadAdapter(
    private val onItemClick: (SimpleDownload) -> Unit
) : RecyclerView.Adapter<SimpleDownloadAdapter.ViewHolder>() {
    
    private var downloads = listOf<SimpleDownload>()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_simple_download, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(downloads[position])
    }
    
    override fun getItemCount(): Int = downloads.size
    
    fun updateDownloads(newDownloads: List<SimpleDownload>) {
        downloads = newDownloads
        notifyDataSetChanged()
    }
    
    fun updateProgress(downloadId: String, progress: Int) {
        val index = downloads.indexOfFirst { it.id == downloadId }
        if (index != -1) {
            notifyItemChanged(index)
        }
    }
    
    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleText: TextView = itemView.findViewById(R.id.titleText)
        private val artistText: TextView = itemView.findViewById(R.id.artistText)
        private val statusText: TextView = itemView.findViewById(R.id.statusText)
        private val progressBar: ProgressBar = itemView.findViewById(R.id.progressBar)
        
        fun bind(download: SimpleDownload) {
            titleText.text = download.title
            artistText.text = download.artist
            
            when (download.status) {
                SimpleDownloadStatus.DOWNLOADING -> {
                    statusText.text = "جاري التحميل... ${download.progress}%"
                    progressBar.visibility = View.VISIBLE
                    progressBar.progress = download.progress
                }
                SimpleDownloadStatus.COMPLETED -> {
                    statusText.text = "مكتمل ✓"
                    progressBar.visibility = View.GONE
                }
                SimpleDownloadStatus.FAILED -> {
                    statusText.text = "فاشل ✗"
                    progressBar.visibility = View.GONE
                }
            }
            
            itemView.setOnClickListener {
                onItemClick(download)
            }
        }
    }
}
