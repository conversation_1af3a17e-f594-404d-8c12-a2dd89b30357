package com.musicplayer.pro.download

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.URL
import java.net.URLDecoder
import java.util.regex.Pattern

/**
 * مستخرج معلومات الوسائط المتقدم
 */
object MediaInfoExtractor {
    
    private const val TAG = "MediaInfoExtractor"
    
    /**
     * استخراج معلومات الوسائط من الرابط
     */
    suspend fun extractInfo(url: String): MediaInfo {
        return withContext(Dispatchers.IO) {
            try {
                when {
                    isYouTubeUrl(url) -> extractYouTubeInfo(url)
                    isSoundCloudUrl(url) -> extractSoundCloudInfo(url)
                    isSpotifyUrl(url) -> extractSpotifyInfo(url)
                    isBandcampUrl(url) -> extractBandcampInfo(url)
                    isMixcloudUrl(url) -> extractMixcloudInfo(url)
                    isDirectAudioUrl(url) -> extractDirectAudioInfo(url)
                    else -> extractGenericInfo(url)
                }
            } catch (e: Exception) {
                Log.e(TAG, "خطأ في استخراج المعلومات: ${e.message}")
                createFallbackInfo(url)
            }
        }
    }
    
    /**
     * استخراج معلومات YouTube
     */
    private suspend fun extractYouTubeInfo(url: String): MediaInfo {
        val videoId = extractYouTubeVideoId(url)
        
        // محاولة استخراج العنوان من الرابط
        val title = try {
            val decodedUrl = URLDecoder.decode(url, "UTF-8")
            when {
                decodedUrl.contains("&t=") -> {
                    // استخراج معرف الفيديو
                    "YouTube Video ($videoId)"
                }
                decodedUrl.contains("list=") -> {
                    "YouTube Playlist Video"
                }
                else -> "YouTube Video"
            }
        } catch (e: Exception) {
            "YouTube Video"
        }
        
        return MediaInfo(
            title = title,
            artist = "YouTube Channel",
            album = "YouTube",
            thumbnailUrl = if (videoId.isNotEmpty()) {
                "https://img.youtube.com/vi/$videoId/maxresdefault.jpg"
            } else {
                ""
            },
            duration = estimateDuration(url, 180),
            estimatedSize = estimateFileSize(180, "mp3"),
            platform = "YouTube"
        )
    }
    
    /**
     * استخراج معلومات SoundCloud
     */
    private suspend fun extractSoundCloudInfo(url: String): MediaInfo {
        val pathParts = url.substringAfter("soundcloud.com/").split("/")
        
        val artist = if (pathParts.isNotEmpty()) {
            pathParts[0].replace("-", " ").replace("_", " ")
                .split(" ").joinToString(" ") { it.capitalize() }
        } else {
            "SoundCloud Artist"
        }
        
        val title = if (pathParts.size > 1) {
            pathParts[1].replace("-", " ").replace("_", " ")
                .split(" ").joinToString(" ") { it.capitalize() }
        } else {
            "SoundCloud Track"
        }
        
        return MediaInfo(
            title = title,
            artist = artist,
            album = "SoundCloud",
            thumbnailUrl = "https://via.placeholder.com/300x300/ff6600/ffffff?text=SoundCloud",
            duration = estimateDuration(url, 240),
            estimatedSize = estimateFileSize(240, "mp3"),
            platform = "SoundCloud"
        )
    }
    
    /**
     * استخراج معلومات Spotify
     */
    private suspend fun extractSpotifyInfo(url: String): MediaInfo {
        // استخراج نوع المحتوى (track, album, playlist)
        val contentType = when {
            url.contains("/track/") -> "Track"
            url.contains("/album/") -> "Album"
            url.contains("/playlist/") -> "Playlist"
            url.contains("/artist/") -> "Artist"
            else -> "Content"
        }
        
        return MediaInfo(
            title = "Spotify $contentType",
            artist = "Spotify Artist",
            album = "Spotify",
            thumbnailUrl = "https://via.placeholder.com/300x300/1db954/ffffff?text=Spotify",
            duration = estimateDuration(url, 200),
            estimatedSize = estimateFileSize(200, "mp3"),
            platform = "Spotify"
        )
    }
    
    /**
     * استخراج معلومات Bandcamp
     */
    private suspend fun extractBandcampInfo(url: String): MediaInfo {
        val parts = url.split("/")
        val trackName = parts.lastOrNull()?.replace("-", " ")?.replace("_", " ") ?: "Bandcamp Track"
        
        return MediaInfo(
            title = trackName.split(" ").joinToString(" ") { it.capitalize() },
            artist = "Bandcamp Artist",
            album = "Bandcamp",
            thumbnailUrl = "https://via.placeholder.com/300x300/629aa0/ffffff?text=Bandcamp",
            duration = estimateDuration(url, 220),
            estimatedSize = estimateFileSize(220, "mp3"),
            platform = "Bandcamp"
        )
    }
    
    /**
     * استخراج معلومات Mixcloud
     */
    private suspend fun extractMixcloudInfo(url: String): MediaInfo {
        val parts = url.split("/")
        val mixName = parts.lastOrNull()?.replace("-", " ")?.replace("_", " ") ?: "Mixcloud Mix"
        
        return MediaInfo(
            title = mixName.split(" ").joinToString(" ") { it.capitalize() },
            artist = "Mixcloud DJ",
            album = "Mixcloud",
            thumbnailUrl = "https://via.placeholder.com/300x300/314359/ffffff?text=Mixcloud",
            duration = estimateDuration(url, 3600), // ساعة واحدة للمزيج
            estimatedSize = estimateFileSize(3600, "mp3"),
            platform = "Mixcloud"
        )
    }
    
    /**
     * استخراج معلومات الملف الصوتي المباشر
     */
    private suspend fun extractDirectAudioInfo(url: String): MediaInfo {
        val fileName = url.substringAfterLast("/").substringBeforeLast(".")
        val extension = url.substringAfterLast(".")
        
        val cleanTitle = fileName.replace("-", " ").replace("_", " ")
            .split(" ").joinToString(" ") { it.capitalize() }
        
        return MediaInfo(
            title = cleanTitle.ifEmpty { "Audio File" },
            artist = "Unknown Artist",
            album = "Direct Download",
            thumbnailUrl = "",
            duration = estimateDuration(url, 210),
            estimatedSize = estimateFileSize(210, extension),
            platform = "Direct"
        )
    }
    
    /**
     * استخراج معلومات عامة
     */
    private suspend fun extractGenericInfo(url: String): MediaInfo {
        val domain = try {
            URL(url).host.replace("www.", "")
        } catch (e: Exception) {
            "Unknown"
        }
        
        return MediaInfo(
            title = "Audio from $domain",
            artist = "Unknown Artist",
            album = domain.capitalize(),
            thumbnailUrl = "",
            duration = estimateDuration(url, 180),
            estimatedSize = estimateFileSize(180, "mp3"),
            platform = domain
        )
    }
    
    /**
     * إنشاء معلومات احتياطية
     */
    private fun createFallbackInfo(url: String): MediaInfo {
        return MediaInfo(
            title = "Unknown Audio",
            artist = "Unknown Artist",
            album = "Unknown Album",
            thumbnailUrl = "",
            duration = 180,
            estimatedSize = estimateFileSize(180, "mp3"),
            platform = "Unknown"
        )
    }
    
    // دوال مساعدة للتحقق من نوع الرابط
    private fun isYouTubeUrl(url: String): Boolean {
        return url.contains("youtube.com") || url.contains("youtu.be")
    }
    
    private fun isSoundCloudUrl(url: String): Boolean {
        return url.contains("soundcloud.com")
    }
    
    private fun isSpotifyUrl(url: String): Boolean {
        return url.contains("spotify.com")
    }
    
    private fun isBandcampUrl(url: String): Boolean {
        return url.contains("bandcamp.com")
    }
    
    private fun isMixcloudUrl(url: String): Boolean {
        return url.contains("mixcloud.com")
    }
    
    private fun isDirectAudioUrl(url: String): Boolean {
        val audioExtensions = listOf("mp3", "mp4", "m4a", "wav", "flac", "ogg", "webm")
        return audioExtensions.any { url.lowercase().endsWith(".$it") }
    }
    
    /**
     * استخراج معرف فيديو YouTube
     */
    private fun extractYouTubeVideoId(url: String): String {
        return try {
            when {
                url.contains("youtu.be/") -> {
                    url.substringAfter("youtu.be/").substringBefore("?").substringBefore("&")
                }
                url.contains("youtube.com/watch?v=") -> {
                    url.substringAfter("v=").substringBefore("&")
                }
                url.contains("youtube.com/embed/") -> {
                    url.substringAfter("embed/").substringBefore("?")
                }
                else -> ""
            }
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * تقدير مدة الملف
     */
    private fun estimateDuration(url: String, defaultDuration: Int): Int {
        // يمكن تحسين هذا لاحقاً لاستخراج المدة الحقيقية
        return when {
            url.contains("mix") || url.contains("playlist") -> (30..120).random() * 60 // 30-120 دقيقة
            url.contains("podcast") -> (15..60).random() * 60 // 15-60 دقيقة
            else -> defaultDuration
        }
    }
    
    /**
     * تقدير حجم الملف
     */
    private fun estimateFileSize(durationSeconds: Int, format: String): Long {
        // تقدير تقريبي بناءً على المدة والتنسيق
        val bitrate = when (format.lowercase()) {
            "mp3" -> 128 // kbps
            "mp4", "m4a" -> 128
            "flac" -> 1000
            "wav" -> 1400
            else -> 128
        }
        
        // حساب الحجم: (bitrate * duration) / 8 / 1024 (MB)
        return ((bitrate * durationSeconds) / 8 / 1024).toLong() * 1024 * 1024 // تحويل إلى bytes
    }
}

/**
 * نموذج معلومات الوسائط
 */
data class MediaInfo(
    val title: String,
    val artist: String,
    val album: String = "",
    val thumbnailUrl: String = "",
    val duration: Int, // بالثواني
    val estimatedSize: Long, // بالبايت
    val platform: String = ""
)
