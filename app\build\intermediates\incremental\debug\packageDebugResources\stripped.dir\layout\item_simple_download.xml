<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- أيقونة الموسيقى -->
        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/bg_thumbnail"
            android:padding="8dp"
            android:src="@drawable/ic_music_note"
            android:scaleType="centerInside"
            app:tint="@color/accent_primary" />

        <!-- المحتوى الرئيسي -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- العنوان -->
            <TextView
                android:id="@+id/titleText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="عنوان الأغنية"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- الفنان -->
            <TextView
                android:id="@+id/artistText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="اسم الفنان"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- الحالة -->
            <TextView
                android:id="@+id/statusText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="جاري التحميل..."
                android:textColor="@color/accent_primary"
                android:textSize="12sp"
                android:textStyle="bold" />

            <!-- شريط التقدم -->
            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_marginTop="8dp"
                android:progressTint="@color/accent_primary"
                android:progressBackgroundTint="@color/background_secondary"
                android:progress="50" />

        </LinearLayout>

        <!-- أيقونة التشغيل -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_play"
            android:padding="4dp"
            app:tint="@color/accent_primary" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
