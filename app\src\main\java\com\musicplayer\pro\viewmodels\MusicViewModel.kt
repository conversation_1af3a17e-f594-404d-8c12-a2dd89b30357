package com.musicplayer.pro.viewmodels

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.musicplayer.pro.models.Song
import com.musicplayer.pro.models.Playlist
import com.musicplayer.pro.models.RepeatMode
import com.musicplayer.pro.repositories.MusicRepository
import com.musicplayer.pro.services.MusicService
import com.musicplayer.pro.utils.MediaScanner
import kotlinx.coroutines.launch

/**
 * ViewModel للموسيقى - يدير البيانات والحالة
 * مطابق لمنطق التطبيق في Python
 */
class MusicViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository = MusicRepository(application)
    private val mediaScanner = MediaScanner(application)

    // Music Service
    private var musicService: MusicService? = null

    // البيانات المباشرة
    private val _songs = MutableLiveData<List<Song>>()
    val songs: LiveData<List<Song>> = _songs
    
    private val _playlists = MutableLiveData<List<Playlist>>()
    val playlists: LiveData<List<Playlist>> = _playlists
    
    private val _currentSong = MutableLiveData<Song?>()
    val currentSong: LiveData<Song?> = _currentSong
    
    private val _isPlaying = MutableLiveData<Boolean>()
    val isPlaying: LiveData<Boolean> = _isPlaying
    
    private val _currentPosition = MutableLiveData<Int>()
    val currentPosition: LiveData<Int> = _currentPosition
    
    private val _duration = MutableLiveData<Int>()
    val duration: LiveData<Int> = _duration
    
    private val _repeatMode = MutableLiveData<RepeatMode>()
    val repeatMode: LiveData<RepeatMode> = _repeatMode
    
    private val _isShuffleEnabled = MutableLiveData<Boolean>()
    val isShuffleEnabled: LiveData<Boolean> = _isShuffleEnabled
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    // قائمة التشغيل الحالية
    private var currentPlaylist: List<Song> = emptyList()
    private var currentIndex: Int = -1
    
    init {
        // تهيئة القيم الافتراضية
        _isPlaying.value = false
        _repeatMode.value = RepeatMode.OFF
        _isShuffleEnabled.value = false
        _isLoading.value = false
    }
    
    /**
     * تحميل جميع الأغاني
     */
    fun loadSongs() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val songList = mediaScanner.scanForMusic()
                _songs.value = songList
                _errorMessage.value = null
            } catch (e: Exception) {
                _errorMessage.value = e.message
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * تحميل قوائم التشغيل
     */
    fun loadPlaylists() {
        viewModelScope.launch {
            try {
                val playlistList = repository.getAllPlaylists()
                _playlists.value = playlistList
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }

    /**
     * تحديث قائمة الأغاني مباشرة
     */
    fun updateSongs(newSongs: List<Song>) {
        _songs.value = newSongs
    }

    /**
     * ربط MusicService
     */
    fun bindMusicService(service: MusicService) {
        musicService = service

        // إعداد المستمعين
        service.setOnSongChangedListener { song ->
            _currentSong.value = song
            // تحديث المدة عند تغيير الأغنية
            _duration.value = service.getDuration()
        }

        service.setOnPlaybackStateChangedListener { isPlaying ->
            _isPlaying.value = isPlaying
        }

        service.setOnPositionChangedListener { position ->
            _currentPosition.value = position
        }

        // تحديث البيانات الحالية من الخدمة
        _currentPosition.value = service.getCurrentPosition()
        _duration.value = service.getDuration()
        _isPlaying.value = service.isPlaying()
        service.getCurrentSong()?.let { song ->
            _currentSong.value = song
        }
    }

    /**
     * تشغيل أغنية
     */
    fun playSong(song: Song, playlist: List<Song> = listOf(song)) {
        currentPlaylist = playlist
        currentIndex = playlist.indexOf(song)
        _currentSong.value = song

        // تشغيل الأغنية عبر MusicService
        musicService?.playSong(song, playlist)
    }
    
    /**
     * تبديل التشغيل/الإيقاف
     */
    fun togglePlayPause() {
        musicService?.togglePlayPause()
    }
    
    /**
     * تشغيل الأغنية التالية
     */
    fun playNext() {
        musicService?.playNext()
    }
    
    /**
     * تشغيل الأغنية السابقة
     */
    fun playPrevious() {
        musicService?.playPrevious()
    }
    
    /**
     * البحث إلى موضع معين
     */
    fun seekTo(position: Int) {
        _currentPosition.value = position
        musicService?.seekTo(position)
    }
    
    /**
     * تبديل التشغيل العشوائي
     */
    fun toggleShuffle() {
        val currentShuffle = _isShuffleEnabled.value ?: false
        _isShuffleEnabled.value = !currentShuffle
        musicService?.setShuffleEnabled(!currentShuffle)
    }
    
    /**
     * تبديل نمط التكرار
     */
    fun toggleRepeat() {
        val currentRepeat = _repeatMode.value ?: RepeatMode.OFF
        val nextRepeat = when (currentRepeat) {
            RepeatMode.OFF -> RepeatMode.ALL
            RepeatMode.ALL -> RepeatMode.ONE
            RepeatMode.ONE -> RepeatMode.OFF
        }
        _repeatMode.value = nextRepeat
        musicService?.setRepeatMode(nextRepeat)
    }
    
    /**
     * تشغيل عشوائي
     */
    fun shuffleAndPlay(songs: List<Song>) {
        if (songs.isNotEmpty()) {
            val shuffledSongs = songs.shuffled()
            playSong(shuffledSongs.first(), shuffledSongs)
            _isShuffleEnabled.value = true
        }
    }
    
    /**
     * تبديل المفضلة للأغنية الحالية
     */
    fun toggleCurrentSongFavorite() {
        _currentSong.value?.let { song ->
            toggleFavorite(song)
        }
    }
    
    /**
     * تبديل المفضلة لأغنية
     */
    fun toggleFavorite(song: Song) {
        viewModelScope.launch {
            try {
                val updatedSong = song.copy(isFavorite = !song.isFavorite)
                repository.updateSong(updatedSong)
                
                // تحديث القائمة الحالية
                val currentSongs = _songs.value?.toMutableList()
                currentSongs?.let { list ->
                    val index = list.indexOfFirst { it.id == song.id }
                    if (index != -1) {
                        list[index] = updatedSong
                        _songs.value = list
                    }
                }
                
                // تحديث الأغنية الحالية إذا كانت نفسها
                if (_currentSong.value?.id == song.id) {
                    _currentSong.value = updatedSong
                }
                
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * البحث في الأغاني
     */
    fun searchSongs(query: String) {
        viewModelScope.launch {
            try {
                val allSongs = _songs.value ?: emptyList()
                val filteredSongs = if (query.isBlank()) {
                    allSongs
                } else {
                    allSongs.filter { song ->
                        song.title.contains(query, ignoreCase = true) ||
                        song.artist.contains(query, ignoreCase = true) ||
                        song.album.contains(query, ignoreCase = true)
                    }
                }
                _songs.value = filteredSongs
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * إنشاء قائمة تشغيل جديدة
     */
    fun createPlaylist(name: String, songs: List<Song> = emptyList()) {
        viewModelScope.launch {
            try {
                val playlist = Playlist(
                    name = name,
                    songs = songs,
                    songCount = songs.size,
                    totalDuration = songs.sumOf { it.duration }
                )
                repository.insertPlaylist(playlist)
                loadPlaylists() // إعادة تحميل القوائم
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * حذف قائمة تشغيل
     */
    fun deletePlaylist(playlist: Playlist) {
        viewModelScope.launch {
            try {
                repository.deletePlaylist(playlist)
                loadPlaylists() // إعادة تحميل القوائم
            } catch (e: Exception) {
                _errorMessage.value = e.message
            }
        }
    }
    
    /**
     * تحديث موضع التشغيل (يتم استدعاؤها من الخدمة)
     */
    fun updatePosition(position: Int) {
        _currentPosition.value = position
    }
    
    /**
     * تحديث مدة الأغنية (يتم استدعاؤها من الخدمة)
     */
    fun updateDuration(duration: Int) {
        _duration.value = duration
    }
    
    /**
     * تحديث حالة التشغيل (يتم استدعاؤها من الخدمة)
     */
    fun updatePlayingState(isPlaying: Boolean) {
        _isPlaying.value = isPlaying
    }

    /**
     * إعادة تحميل مكتبة الأغاني (لإظهار الأغاني المحملة الجديدة)
     */
    fun refreshMusicLibrary() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // إعادة تحميل الأغاني من النظام
                loadSongs()
                android.util.Log.d("MusicViewModel", "تم تحديث مكتبة الأغاني")
            } catch (e: Exception) {
                _errorMessage.value = e.message
                android.util.Log.e("MusicViewModel", "خطأ في تحديث مكتبة الأغاني: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * مسح رسالة الخطأ
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * تحديث قائمة الأغاني
     */
    fun refreshSongs() {
        viewModelScope.launch {
            try {
                android.util.Log.d("MusicViewModel", "تحديث قائمة الأغاني...")
                // إعادة فحص الأغاني
                loadSongs()
            } catch (e: Exception) {
                android.util.Log.e("MusicViewModel", "خطأ في تحديث الأغاني: ${e.message}")
                _errorMessage.value = "خطأ في تحديث الأغاني: ${e.message}"
            }
        }
    }
}
